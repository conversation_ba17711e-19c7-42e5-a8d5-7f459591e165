# 文件组织与存储模块

## 模块概述
本模块负责将抓取的内容按照网站菜单结构组织并存储为本地Markdown文件，确保文件结构清晰、命名规范。

## 功能要求

### 1. 目录结构管理
- **层级映射**：
  - 严格按照网站菜单层级创建目录
  - 保持原始文档的逻辑结构
  - 支持多级嵌套目录（最多5级深度）
  - 目录层级与菜单层级一一对应

- **目录命名**：
  - 目录名添加序号前缀（如`01-概述`、`02-快速开始`）
  - 支持中文目录名和特殊字符处理
  - 自动处理文件系统不支持的字符
  - 保持语义清晰的同时确保系统兼容性

### 2. 文件命名规则
- **命名策略**：
  - 基于页面标题生成文件名
  - 添加序号前缀保持顺序
  - 自动生成合法的文件名（去除特殊字符）
  - 处理重名冲突（添加数字后缀）

- **字符处理**：
  - 移除或替换文件系统不支持的字符：`< > : " | ? * /`
  - 中文字符保留，确保可读性
  - 长文件名截断处理（保持在255字符以内）
  - 保持原有语义的同时确保文件系统兼容性

### 3. 文件内容组织
- **Markdown文件结构**：
  - 文件头部包含元信息（标题、URL、抓取时间等）
  - 保持原始内容的格式和结构
  - 添加导航链接（上一页、下一页、返回目录）
  - 统一的文件编码（UTF-8）

- **内容完整性**：
  - 确保所有文本内容正确保存
  - 代码块、表格等特殊格式保持完整
  - 图片链接和外部链接保持有效

### 4. README生成
- **目录索引**：
  - 自动生成完整的目录索引文件
  - 包含文档概述、目录结构、更新时间等信息
  - 支持多级目录的树状显示
  - 提供快速导航链接

- **统计信息**：
  - 文档总数统计
  - 目录层级统计
  - 抓取完成时间
  - 数据来源和版本信息

## 技术实现

### 1. 核心依赖库
- **文件处理**：pathlib（现代化路径处理）
- **编码处理**：基础UTF-8编码支持
- **文本处理**：正则表达式和字符串操作

### 2. 存储策略
- **目录创建**：
  - 递归创建多级目录结构
  - 检查目录权限和磁盘空间
  - 处理目录创建失败的异常情况

- **文件写入**：
  - 原子性写入操作（先写临时文件再重命名）
  - 文件锁机制防止并发写入冲突
  - 写入失败的回滚机制

### 3. 数据结构
- **文件树**：树形结构管理文件和目录关系
- **元数据**：文件信息、创建时间、来源URL等
- **索引映射**：URL到本地文件路径的映射关系

### 4. 存储流程
1. **路径规划**：根据菜单结构规划目录和文件路径
2. **目录创建**：递归创建所需的目录结构
3. **文件命名**：生成唯一且合法的文件名
4. **内容写入**：将Markdown内容写入文件
5. **索引更新**：更新目录索引和导航文件
6. **完整性检查**：验证文件写入的完整性

## 质量保证

### 1. 文件系统兼容性
- **跨平台支持**：Windows 10/11文件系统兼容
- **字符编码**：统一使用UTF-8编码
- **路径长度**：处理Windows路径长度限制
- **特殊字符**：正确处理文件名中的特殊字符

### 2. 数据完整性
- **原子操作**：确保文件写入的原子性
- **错误恢复**：写入失败时的数据恢复机制
- **校验机制**：文件内容完整性校验
- **备份策略**：重要文件的备份机制

### 3. 性能优化
- **批量操作**：减少文件系统调用次数
- **内存管理**：大文件的流式写入
- **并发安全**：多线程环境下的文件操作安全

### 4. 可维护性
- **清晰结构**：目录结构清晰易懂
- **命名规范**：统一的文件和目录命名规则
- **文档完整**：README文件提供完整的导航信息
- **扩展性**：为未来功能扩展预留空间