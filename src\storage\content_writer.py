#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容写入器

负责将Markdown内容写入文件，包括元数据添加、导航链接生成、原子写入等功能。
确保文件内容的完整性和一致性。

作者: Assistant
创建时间: 2025-01-27
"""

import os
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import hashlib

from .base import StorageConfig, StorageComponent, StorageError, FileInfo


class ContentWriter(StorageComponent):
    """内容写入器"""
    
    def __init__(self):
        super().__init__("ContentWriter")
    
    def process(self, file_info: FileInfo, config: StorageConfig) -> Path:
        """写入文件内容
        
        Args:
            file_info: 文件信息
            config: 存储配置
            
        Returns:
            写入的文件路径
        """
        if not file_info.absolute_path:
            raise StorageError("文件路径未设置", "invalid_path")
        
        # 生成完整的文件内容
        full_content = self._generate_full_content(file_info, config)
        
        # 写入文件
        if config.atomic_write:
            return self._atomic_write(file_info.absolute_path, full_content, config)
        else:
            return self._direct_write(file_info.absolute_path, full_content, config)
    
    def _generate_full_content(self, file_info: FileInfo, config: StorageConfig) -> str:
        """生成完整的文件内容
        
        Args:
            file_info: 文件信息
            config: 存储配置
            
        Returns:
            完整的文件内容
        """
        content_parts = []
        
        # 添加元数据头部
        if config.add_metadata:
            metadata_header = self._generate_metadata_header(file_info)
            content_parts.append(metadata_header)
        
        # 添加导航链接
        if config.add_navigation:
            navigation = self._generate_navigation(file_info)
            if navigation:
                content_parts.append(navigation)
        
        # 添加主要内容
        if file_info.content:
            content_parts.append(file_info.content)
        
        # 添加页脚信息
        footer = self._generate_footer(file_info)
        if footer:
            content_parts.append(footer)
        
        return "\n\n".join(content_parts)
    
    def _generate_metadata_header(self, file_info: FileInfo) -> str:
        """生成元数据头部
        
        Args:
            file_info: 文件信息
            
        Returns:
            元数据头部内容
        """
        lines = ["---"]
        
        # 基础信息
        if file_info.title:
            lines.append(f"title: \"{file_info.title}\"")
        
        if file_info.url:
            lines.append(f"source_url: \"{file_info.url}\"")
        
        # 时间信息
        if file_info.created_time:
            lines.append(f"created_at: \"{file_info.created_time.isoformat()}\"")
        
        lines.append(f"scraped_at: \"{datetime.now().isoformat()}\"")
        
        # 层级信息
        lines.append(f"level: {file_info.level}")
        lines.append(f"index: {file_info.index}")
        
        # 文件信息
        if file_info.file_size > 0:
            lines.append(f"content_size: {file_info.file_size}")
        
        # 链接统计
        if file_info.internal_links:
            lines.append(f"internal_links_count: {len(file_info.internal_links)}")
        if file_info.external_links:
            lines.append(f"external_links_count: {len(file_info.external_links)}")
        if file_info.images:
            lines.append(f"images_count: {len(file_info.images)}")
        
        # 自定义元数据
        for key, value in file_info.metadata.items():
            if isinstance(value, str):
                lines.append(f"{key}: \"{value}\"")
            else:
                lines.append(f"{key}: {value}")
        
        lines.append("---")
        return "\n".join(lines)
    
    def _generate_navigation(self, file_info: FileInfo) -> Optional[str]:
        """生成导航链接
        
        Args:
            file_info: 文件信息
            
        Returns:
            导航链接内容
        """
        # 这里可以根据需要添加上一页、下一页、返回目录等链接
        # 目前先返回基础的导航信息
        nav_parts = []
        
        # 添加层级信息
        if file_info.level > 0:
            nav_parts.append(f"📍 **当前位置**: 第{file_info.level}层 - 第{file_info.index}项")
        
        # 添加原始链接
        if file_info.url:
            nav_parts.append(f"🔗 **原始链接**: [{file_info.url}]({file_info.url})")
        
        if nav_parts:
            return "\n".join(nav_parts)
        
        return None
    
    def _generate_footer(self, file_info: FileInfo) -> Optional[str]:
        """生成页脚信息
        
        Args:
            file_info: 文件信息
            
        Returns:
            页脚内容
        """
        footer_parts = []
        
        # 添加统计信息
        if file_info.internal_links or file_info.external_links or file_info.images:
            footer_parts.append("---")
            footer_parts.append("## 📊 页面统计")
            
            if file_info.internal_links:
                footer_parts.append(f"- 内部链接: {len(file_info.internal_links)}个")
            if file_info.external_links:
                footer_parts.append(f"- 外部链接: {len(file_info.external_links)}个")
            if file_info.images:
                footer_parts.append(f"- 图片: {len(file_info.images)}个")
        
        # 添加抓取信息
        footer_parts.append("")
        footer_parts.append(f"*本文档由技术文档抓取工具自动生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        
        if footer_parts:
            return "\n".join(footer_parts)
        
        return None
    
    def _atomic_write(self, file_path: Path, content: str, config: StorageConfig) -> Path:
        """原子写入文件
        
        Args:
            file_path: 目标文件路径
            content: 文件内容
            config: 存储配置
            
        Returns:
            写入的文件路径
        """
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 备份现有文件
        backup_path = None
        if file_path.exists() and config.backup_existing:
            backup_path = self._create_backup(file_path)
        
        # 创建临时文件
        temp_dir = file_path.parent
        temp_prefix = f".{file_path.name}.tmp"
        
        try:
            with tempfile.NamedTemporaryFile(
                mode='w', 
                encoding=config.encoding,
                dir=temp_dir,
                prefix=temp_prefix,
                delete=False
            ) as temp_file:
                temp_path = Path(temp_file.name)
                temp_file.write(content)
                temp_file.flush()
                os.fsync(temp_file.fileno())  # 确保写入磁盘
            
            # 原子替换
            if os.name == 'nt':  # Windows
                if file_path.exists():
                    file_path.unlink()
                temp_path.rename(file_path)
            else:  # Unix-like
                temp_path.rename(file_path)
            
            # 验证写入结果
            if not self._verify_write(file_path, content, config):
                raise StorageError("文件写入验证失败", "write_verification", str(file_path))
            
            return file_path
            
        except Exception as e:
            # 清理临时文件
            if temp_path and temp_path.exists():
                try:
                    temp_path.unlink()
                except:
                    pass
            
            # 恢复备份
            if backup_path and backup_path.exists():
                try:
                    shutil.copy2(backup_path, file_path)
                except:
                    pass
            
            raise StorageError(f"原子写入失败: {e}", "atomic_write", str(file_path))
        
        finally:
            # 清理备份文件
            if backup_path and backup_path.exists() and not config.backup_existing:
                try:
                    backup_path.unlink()
                except:
                    pass
    
    def _direct_write(self, file_path: Path, content: str, config: StorageConfig) -> Path:
        """直接写入文件
        
        Args:
            file_path: 目标文件路径
            content: 文件内容
            config: 存储配置
            
        Returns:
            写入的文件路径
        """
        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 备份现有文件
            if file_path.exists() and config.backup_existing:
                self._create_backup(file_path)
            
            # 写入文件
            with open(file_path, 'w', encoding=config.encoding) as f:
                f.write(content)
            
            return file_path
            
        except Exception as e:
            raise StorageError(f"直接写入失败: {e}", "direct_write", str(file_path))
    
    def _create_backup(self, file_path: Path) -> Path:
        """创建备份文件
        
        Args:
            file_path: 原文件路径
            
        Returns:
            备份文件路径
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.stem}.backup_{timestamp}{file_path.suffix}"
        backup_path = file_path.parent / backup_name
        
        try:
            shutil.copy2(file_path, backup_path)
            return backup_path
        except Exception as e:
            raise StorageError(f"创建备份失败: {e}", "backup_creation", str(file_path))
    
    def _verify_write(self, file_path: Path, expected_content: str, config: StorageConfig) -> bool:
        """验证文件写入结果
        
        Args:
            file_path: 文件路径
            expected_content: 期望的内容
            config: 存储配置
            
        Returns:
            验证是否通过
        """
        try:
            if not file_path.exists():
                return False
            
            # 读取文件内容
            with open(file_path, 'r', encoding=config.encoding) as f:
                actual_content = f.read()
            
            # 比较内容长度
            if len(actual_content) != len(expected_content):
                return False
            
            # 比较内容哈希（更高效）
            expected_hash = hashlib.md5(expected_content.encode(config.encoding)).hexdigest()
            actual_hash = hashlib.md5(actual_content.encode(config.encoding)).hexdigest()
            
            return expected_hash == actual_hash
            
        except Exception:
            return False
    
    def batch_write(self, file_infos: List[FileInfo], config: StorageConfig) -> List[Path]:
        """批量写入文件
        
        Args:
            file_infos: 文件信息列表
            config: 存储配置
            
        Returns:
            写入的文件路径列表
        """
        written_paths = []
        errors = []
        
        for file_info in file_infos:
            try:
                path = self.process(file_info, config)
                written_paths.append(path)
            except Exception as e:
                errors.append(f"写入失败 {file_info.filename}: {e}")
        
        if errors:
            error_msg = "; ".join(errors)
            raise StorageError(f"批量写入部分失败: {error_msg}", "batch_write")
        
        return written_paths