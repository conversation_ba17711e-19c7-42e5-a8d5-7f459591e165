#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
框架管理器模块

本模块提供框架管理功能，负责注册、检测和调用不同的文档框架。
支持动态框架注册和自动框架检测，为上层应用提供统一的框架访问接口。

主要功能:
1. 框架注册管理 - 动态注册和管理多种文档框架
2. 自动框架检测 - 根据网站内容自动识别使用的框架
3. 统一接口调用 - 为不同框架提供统一的调用接口
4. 框架优先级管理 - 支持框架检测优先级配置

支持的框架:
- Astro+Starlight: 现代文档网站框架
- 可扩展支持更多框架

作者: Assistant
创建时间: 2024
"""

import logging
from typing import List, Optional, Dict, Any, Type

from .base import DocumentFramework, PageInfo, ContentInfo
from .starlight import StarlightFramework


class FrameworkManager:
    """框架管理器
    
    负责管理所有已注册的文档框架，提供框架检测、注册和调用功能。
    采用单例模式确保全局唯一的框架管理实例。
    """
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化框架管理器"""
        if self._initialized:
            return
        
        self.logger = logging.getLogger(__name__ + '.FrameworkManager')
        self._frameworks: Dict[str, DocumentFramework] = {}
        self._framework_classes: Dict[str, Type[DocumentFramework]] = {}
        self._detection_order: List[str] = []
        
        # 注册默认框架
        self._register_default_frameworks()
        
        self._initialized = True
        self.logger.info("框架管理器初始化完成")
    
    def _register_default_frameworks(self):
        """注册默认支持的框架"""
        # 注册Starlight框架
        self.register_framework('starlight', StarlightFramework)
        self.logger.debug("已注册默认框架: Starlight")
    
    def register_framework(self, name: str, framework_class: Type[DocumentFramework], 
                          priority: int = 0) -> bool:
        """注册框架
        
        Args:
            name: 框架名称（唯一标识）
            framework_class: 框架类
            priority: 检测优先级（数值越大优先级越高）
            
        Returns:
            bool: 注册是否成功
        """
        try:
            if name in self._framework_classes:
                self.logger.warning(f"框架 '{name}' 已存在，将被覆盖")
            
            # 验证框架类
            if not issubclass(framework_class, DocumentFramework):
                self.logger.error(f"框架类 '{framework_class}' 必须继承自 DocumentFramework")
                return False
            
            # 注册框架类
            self._framework_classes[name] = framework_class
            
            # 更新检测顺序（按优先级排序）
            if name not in self._detection_order:
                self._detection_order.append(name)
            
            # 按优先级重新排序（这里简化处理，实际可以更复杂）
            self._detection_order.sort(key=lambda x: priority, reverse=True)
            
            self.logger.info(f"框架 '{name}' 注册成功，优先级: {priority}")
            return True
            
        except Exception as e:
            self.logger.error(f"注册框架 '{name}' 失败: {e}")
            return False
    
    def unregister_framework(self, name: str) -> bool:
        """注销框架
        
        Args:
            name: 框架名称
            
        Returns:
            bool: 注销是否成功
        """
        try:
            if name not in self._framework_classes:
                self.logger.warning(f"框架 '{name}' 不存在")
                return False
            
            # 移除框架类
            del self._framework_classes[name]
            
            # 移除框架实例（如果存在）
            if name in self._frameworks:
                del self._frameworks[name]
            
            # 从检测顺序中移除
            if name in self._detection_order:
                self._detection_order.remove(name)
            
            self.logger.info(f"框架 '{name}' 注销成功")
            return True
            
        except Exception as e:
            self.logger.error(f"注销框架 '{name}' 失败: {e}")
            return False
    
    def get_framework(self, name: str) -> Optional[DocumentFramework]:
        """获取框架实例
        
        Args:
            name: 框架名称
            
        Returns:
            Optional[DocumentFramework]: 框架实例，不存在返回None
        """
        try:
            # 如果实例已存在，直接返回
            if name in self._frameworks:
                return self._frameworks[name]
            
            # 如果框架类已注册，创建实例
            if name in self._framework_classes:
                framework_class = self._framework_classes[name]
                framework_instance = framework_class()
                self._frameworks[name] = framework_instance
                self.logger.debug(f"创建框架实例: {name}")
                return framework_instance
            
            self.logger.warning(f"框架 '{name}' 未注册")
            return None
            
        except Exception as e:
            self.logger.error(f"获取框架 '{name}' 失败: {e}")
            return None
    
    def detect_framework(self, url: str, html_content: str) -> Optional[str]:
        """自动检测网站使用的框架
        
        Args:
            url: 网站URL
            html_content: HTML内容
            
        Returns:
            Optional[str]: 检测到的框架名称，未检测到返回None
        """
        try:
            self.logger.debug(f"开始检测框架: {url}")
            
            # 按优先级顺序检测
            for framework_name in self._detection_order:
                framework = self.get_framework(framework_name)
                if not framework:
                    continue
                
                try:
                    if framework.detect_framework(url, html_content):
                        self.logger.info(f"检测到框架: {framework_name} ({url})")
                        return framework_name
                except Exception as e:
                    self.logger.warning(f"框架 '{framework_name}' 检测失败: {e}")
                    continue
            
            self.logger.info(f"未检测到支持的框架: {url}")
            return None
            
        except Exception as e:
            self.logger.error(f"框架检测失败 {url}: {e}")
            return None
    
    def detect_framework_with_confidence(self, url: str, html_content: str) -> List[Dict[str, Any]]:
        """检测框架并返回置信度信息
        
        Args:
            url: 网站URL
            html_content: HTML内容
            
        Returns:
            List[Dict[str, Any]]: 检测结果列表，包含框架名称和置信度
        """
        results = []
        
        try:
            for framework_name in self._detection_order:
                framework = self.get_framework(framework_name)
                if not framework:
                    continue
                
                try:
                    detector = framework.get_detector()
                    confidence = detector.get_confidence_score(url, html_content)
                    
                    if confidence > 0:
                        results.append({
                            'framework': framework_name,
                            'confidence': confidence,
                            'detected': confidence >= 0.5  # 阈值可配置
                        })
                        
                except Exception as e:
                    self.logger.warning(f"框架 '{framework_name}' 置信度检测失败: {e}")
                    continue
            
            # 按置信度排序
            results.sort(key=lambda x: x['confidence'], reverse=True)
            
        except Exception as e:
            self.logger.error(f"置信度检测失败 {url}: {e}")
        
        return results
    
    def parse_menu_structure(self, framework_name: str, url: str, html_content: str) -> List[PageInfo]:
        """解析菜单结构
        
        Args:
            framework_name: 框架名称
            url: 网站URL
            html_content: HTML内容
            
        Returns:
            List[PageInfo]: 页面信息列表
        """
        try:
            framework = self.get_framework(framework_name)
            if not framework:
                self.logger.error(f"框架 '{framework_name}' 不存在")
                return []
            
            pages = framework.parse_menu_structure(url, html_content)
            self.logger.info(f"菜单解析完成: {len(pages)} 个页面 ({framework_name})")
            return pages
            
        except Exception as e:
            self.logger.error(f"菜单解析失败 {url} ({framework_name}): {e}")
            return []
    
    def extract_page_content(self, framework_name: str, url: str, html_content: str) -> Optional[ContentInfo]:
        """提取页面内容
        
        Args:
            framework_name: 框架名称
            url: 页面URL
            html_content: HTML内容
            
        Returns:
            Optional[ContentInfo]: 内容信息
        """
        try:
            framework = self.get_framework(framework_name)
            if not framework:
                self.logger.error(f"框架 '{framework_name}' 不存在")
                return None
            
            content = framework.extract_page_content(url, html_content)
            if content:
                self.logger.debug(f"内容提取成功: {content.title} ({framework_name})")
            else:
                self.logger.warning(f"内容提取失败: {url} ({framework_name})")
            
            return content
            
        except Exception as e:
            self.logger.error(f"内容提取失败 {url} ({framework_name}): {e}")
            return None
    
    def get_registered_frameworks(self) -> List[str]:
        """获取已注册的框架列表
        
        Returns:
            List[str]: 框架名称列表
        """
        return list(self._framework_classes.keys())
    
    def get_framework_info(self, name: str) -> Optional[Dict[str, Any]]:
        """获取框架信息
        
        Args:
            name: 框架名称
            
        Returns:
            Optional[Dict[str, Any]]: 框架信息字典
        """
        if name not in self._framework_classes:
            return None
        
        framework = self.get_framework(name)
        if not framework:
            return None
        
        return {
            'name': name,
            'class': self._framework_classes[name].__name__,
            'framework_name': framework.get_name(),
            'instance_created': name in self._frameworks
        }
    
    def clear_instances(self):
        """清理所有框架实例（保留注册信息）"""
        self._frameworks.clear()
        self.logger.info("已清理所有框架实例")
    
    def reset(self):
        """重置管理器（清理所有注册信息和实例）"""
        self._frameworks.clear()
        self._framework_classes.clear()
        self._detection_order.clear()
        
        # 重新注册默认框架
        self._register_default_frameworks()
        
        self.logger.info("框架管理器已重置")
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"FrameworkManager(frameworks={len(self._framework_classes)}, instances={len(self._frameworks)})"


# 全局框架管理器实例
_framework_manager = None


def get_framework_manager() -> FrameworkManager:
    """获取全局框架管理器实例
    
    Returns:
        FrameworkManager: 框架管理器实例
    """
    global _framework_manager
    if _framework_manager is None:
        _framework_manager = FrameworkManager()
    return _framework_manager


# 便捷函数
def detect_framework(url: str, html_content: str) -> Optional[str]:
    """检测网站框架（便捷函数）
    
    Args:
        url: 网站URL
        html_content: HTML内容
        
    Returns:
        Optional[str]: 框架名称
    """
    manager = get_framework_manager()
    return manager.detect_framework(url, html_content)


def parse_menu_structure(framework_name: str, url: str, html_content: str) -> List[PageInfo]:
    """解析菜单结构（便捷函数）
    
    Args:
        framework_name: 框架名称
        url: 网站URL
        html_content: HTML内容
        
    Returns:
        List[PageInfo]: 页面信息列表
    """
    manager = get_framework_manager()
    return manager.parse_menu_structure(framework_name, url, html_content)


def extract_page_content(framework_name: str, url: str, html_content: str) -> Optional[ContentInfo]:
    """提取页面内容（便捷函数）
    
    Args:
        framework_name: 框架名称
        url: 页面URL
        html_content: HTML内容
        
    Returns:
        Optional[ContentInfo]: 内容信息
    """
    manager = get_framework_manager()
    return manager.extract_page_content(framework_name, url, html_content)


# 导出的公共接口
__all__ = [
    'FrameworkManager',
    'get_framework_manager',
    'detect_framework',
    'parse_menu_structure', 
    'extract_page_content'
]