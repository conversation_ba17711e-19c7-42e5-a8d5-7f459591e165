#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源管理模块

本模块提供统一的资源管理功能，包括内存优化、线程池管理和异常处理。
"""

import gc
import os
import sys
import psutil
import threading
import time
from typing import Optional, Dict, Any, Callable, List, Union
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from contextlib import contextmanager
from dataclasses import dataclass
from enum import Enum
import weakref
from functools import wraps


class ResourceType(Enum):
    """资源类型枚举"""
    MEMORY = 'memory'
    THREAD = 'thread'
    FILE = 'file'
    NETWORK = 'network'
    OTHER = 'other'


@dataclass
class ResourceInfo:
    """资源信息"""
    resource_id: str
    resource_type: ResourceType
    created_time: float
    last_used_time: float
    usage_count: int = 0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class MemoryStats:
    """内存统计信息"""
    total_memory: int  # 总内存（字节）
    available_memory: int  # 可用内存（字节）
    used_memory: int  # 已用内存（字节）
    memory_percent: float  # 内存使用百分比
    process_memory: int  # 进程内存使用（字节）
    process_memory_percent: float  # 进程内存使用百分比


class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, warning_threshold: float = 80.0, critical_threshold: float = 90.0):
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.callbacks: List[Callable[[MemoryStats], None]] = []
        self.process = psutil.Process()
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
    
    def get_memory_stats(self) -> MemoryStats:
        """获取内存统计信息"""
        # 系统内存信息
        memory = psutil.virtual_memory()
        
        # 进程内存信息
        process_memory = self.process.memory_info()
        
        return MemoryStats(
            total_memory=memory.total,
            available_memory=memory.available,
            used_memory=memory.used,
            memory_percent=memory.percent,
            process_memory=process_memory.rss,
            process_memory_percent=(process_memory.rss / memory.total) * 100
        )
    
    def add_callback(self, callback: Callable[[MemoryStats], None]):
        """添加内存状态回调"""
        if callback not in self.callbacks:
            self.callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[MemoryStats], None]):
        """移除内存状态回调"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def start_monitoring(self, interval: float = 5.0):
        """开始内存监控"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._stop_event.clear()
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self._monitor_thread.start()
    
    def stop_monitoring(self):
        """停止内存监控"""
        if not self._monitoring:
            return
        
        self._monitoring = False
        self._stop_event.set()
        
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1.0)
            self._monitor_thread = None
    
    def _monitor_loop(self, interval: float):
        """监控循环"""
        while not self._stop_event.wait(interval):
            try:
                stats = self.get_memory_stats()
                
                # 检查阈值
                if stats.memory_percent >= self.critical_threshold:
                    self._trigger_callbacks(stats)
                    # 执行紧急内存清理
                    self._emergency_cleanup()
                elif stats.memory_percent >= self.warning_threshold:
                    self._trigger_callbacks(stats)
                
            except Exception:
                pass  # 忽略监控错误
    
    def _trigger_callbacks(self, stats: MemoryStats):
        """触发回调函数"""
        for callback in self.callbacks[:]:
            try:
                callback(stats)
            except Exception:
                pass  # 忽略回调错误
    
    def _emergency_cleanup(self):
        """紧急内存清理"""
        # 强制垃圾回收
        gc.collect()
        
        # 清理缓存（如果有的话）
        try:
            # 这里可以添加应用特定的缓存清理逻辑
            pass
        except Exception:
            pass


class ThreadPoolManager:
    """线程池管理器"""
    
    def __init__(self, max_workers: Optional[int] = None, thread_name_prefix: str = 'Worker'):
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)
        self.thread_name_prefix = thread_name_prefix
        self.executor: Optional[ThreadPoolExecutor] = None
        self.active_futures: weakref.WeakSet = weakref.WeakSet()
        self._lock = threading.RLock()
        self._shutdown = False
    
    def get_executor(self) -> ThreadPoolExecutor:
        """获取线程池执行器"""
        with self._lock:
            if self.executor is None or self._shutdown:
                if self.executor:
                    self.executor.shutdown(wait=False)
                
                self.executor = ThreadPoolExecutor(
                    max_workers=self.max_workers,
                    thread_name_prefix=self.thread_name_prefix
                )
                self._shutdown = False
            
            return self.executor
    
    def submit(self, fn: Callable, *args, **kwargs) -> Future:
        """提交任务到线程池"""
        executor = self.get_executor()
        future = executor.submit(fn, *args, **kwargs)
        self.active_futures.add(future)
        return future
    
    def map(self, fn: Callable, *iterables, timeout: Optional[float] = None, chunksize: int = 1):
        """映射函数到多个参数"""
        executor = self.get_executor()
        return executor.map(fn, *iterables, timeout=timeout, chunksize=chunksize)
    
    def shutdown(self, wait: bool = True, timeout: Optional[float] = None):
        """关闭线程池"""
        with self._lock:
            if self.executor and not self._shutdown:
                self._shutdown = True
                
                # 取消未完成的任务
                for future in list(self.active_futures):
                    if not future.done():
                        future.cancel()
                
                # 关闭执行器
                self.executor.shutdown(wait=wait)
                
                # 等待超时
                if wait and timeout:
                    start_time = time.time()
                    while time.time() - start_time < timeout:
                        if all(future.done() for future in self.active_futures):
                            break
                        time.sleep(0.1)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取线程池统计信息"""
        with self._lock:
            if not self.executor:
                return {
                    'max_workers': self.max_workers,
                    'active_threads': 0,
                    'active_futures': 0,
                    'shutdown': True
                }
            
            return {
                'max_workers': self.max_workers,
                'active_threads': len(self.executor._threads) if hasattr(self.executor, '_threads') else 0,
                'active_futures': len(self.active_futures),
                'shutdown': self._shutdown
            }


class ResourceTracker:
    """资源跟踪器"""
    
    def __init__(self):
        self.resources: Dict[str, ResourceInfo] = {}
        self._lock = threading.RLock()
        self._cleanup_callbacks: Dict[str, Callable] = {}
    
    def register_resource(self, resource_id: str, resource_type: ResourceType,
                         cleanup_callback: Optional[Callable] = None,
                         metadata: Optional[Dict[str, Any]] = None) -> bool:
        """注册资源"""
        with self._lock:
            if resource_id in self.resources:
                return False
            
            current_time = time.time()
            self.resources[resource_id] = ResourceInfo(
                resource_id=resource_id,
                resource_type=resource_type,
                created_time=current_time,
                last_used_time=current_time,
                metadata=metadata or {}
            )
            
            if cleanup_callback:
                self._cleanup_callbacks[resource_id] = cleanup_callback
            
            return True
    
    def unregister_resource(self, resource_id: str) -> bool:
        """注销资源"""
        with self._lock:
            if resource_id not in self.resources:
                return False
            
            # 执行清理回调
            if resource_id in self._cleanup_callbacks:
                try:
                    self._cleanup_callbacks[resource_id]()
                except Exception:
                    pass  # 忽略清理错误
                del self._cleanup_callbacks[resource_id]
            
            del self.resources[resource_id]
            return True
    
    def update_usage(self, resource_id: str):
        """更新资源使用信息"""
        with self._lock:
            if resource_id in self.resources:
                resource = self.resources[resource_id]
                resource.last_used_time = time.time()
                resource.usage_count += 1
    
    def get_resource_info(self, resource_id: str) -> Optional[ResourceInfo]:
        """获取资源信息"""
        with self._lock:
            return self.resources.get(resource_id)
    
    def get_resources_by_type(self, resource_type: ResourceType) -> List[ResourceInfo]:
        """按类型获取资源"""
        with self._lock:
            return [info for info in self.resources.values() 
                   if info.resource_type == resource_type]
    
    def cleanup_unused_resources(self, max_idle_time: float = 3600.0) -> int:
        """清理未使用的资源"""
        current_time = time.time()
        cleanup_count = 0
        
        with self._lock:
            # 找出需要清理的资源
            to_cleanup = []
            for resource_id, info in self.resources.items():
                if current_time - info.last_used_time > max_idle_time:
                    to_cleanup.append(resource_id)
            
            # 执行清理
            for resource_id in to_cleanup:
                if self.unregister_resource(resource_id):
                    cleanup_count += 1
        
        return cleanup_count
    
    def get_stats(self) -> Dict[str, Any]:
        """获取资源统计信息"""
        with self._lock:
            stats = {
                'total_resources': len(self.resources),
                'by_type': {}
            }
            
            # 按类型统计
            for info in self.resources.values():
                type_name = info.resource_type.value
                if type_name not in stats['by_type']:
                    stats['by_type'][type_name] = 0
                stats['by_type'][type_name] += 1
            
            return stats


class ResourceManager:
    """资源管理器主类"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.memory_monitor = MemoryMonitor()
        self.thread_pool_manager = ThreadPoolManager()
        self.resource_tracker = ResourceTracker()
        self._cleanup_thread: Optional[threading.Thread] = None
        self._cleanup_stop_event = threading.Event()
        self._cleanup_interval = 300.0  # 5分钟
        
        # 启动资源清理线程
        self.start_cleanup_thread()
    
    def start_cleanup_thread(self):
        """启动资源清理线程"""
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            return
        
        self._cleanup_stop_event.clear()
        self._cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            daemon=True
        )
        self._cleanup_thread.start()
    
    def stop_cleanup_thread(self):
        """停止资源清理线程"""
        if self._cleanup_thread:
            self._cleanup_stop_event.set()
            self._cleanup_thread.join(timeout=1.0)
            self._cleanup_thread = None
    
    def _cleanup_loop(self):
        """资源清理循环"""
        while not self._cleanup_stop_event.wait(self._cleanup_interval):
            try:
                # 清理未使用的资源
                self.resource_tracker.cleanup_unused_resources()
                
                # 执行垃圾回收
                gc.collect()
                
            except Exception:
                pass  # 忽略清理错误
    
    def get_memory_stats(self) -> MemoryStats:
        """获取内存统计信息"""
        return self.memory_monitor.get_memory_stats()
    
    def start_memory_monitoring(self, interval: float = 5.0,
                              warning_threshold: float = 80.0,
                              critical_threshold: float = 90.0):
        """启动内存监控"""
        self.memory_monitor.warning_threshold = warning_threshold
        self.memory_monitor.critical_threshold = critical_threshold
        self.memory_monitor.start_monitoring(interval)
    
    def stop_memory_monitoring(self):
        """停止内存监控"""
        self.memory_monitor.stop_monitoring()
    
    def submit_task(self, fn: Callable, *args, **kwargs) -> Future:
        """提交任务到线程池"""
        return self.thread_pool_manager.submit(fn, *args, **kwargs)
    
    def register_resource(self, resource_id: str, resource_type: ResourceType,
                         cleanup_callback: Optional[Callable] = None,
                         metadata: Optional[Dict[str, Any]] = None) -> bool:
        """注册资源"""
        return self.resource_tracker.register_resource(
            resource_id, resource_type, cleanup_callback, metadata
        )
    
    def unregister_resource(self, resource_id: str) -> bool:
        """注销资源"""
        return self.resource_tracker.unregister_resource(resource_id)
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        return {
            'memory': self.get_memory_stats().__dict__,
            'thread_pool': self.thread_pool_manager.get_stats(),
            'resources': self.resource_tracker.get_stats()
        }
    
    def shutdown(self, timeout: Optional[float] = None):
        """关闭资源管理器"""
        # 停止监控
        self.stop_memory_monitoring()
        self.stop_cleanup_thread()
        
        # 关闭线程池
        self.thread_pool_manager.shutdown(wait=True, timeout=timeout)
        
        # 清理所有资源
        with self.resource_tracker._lock:
            resource_ids = list(self.resource_tracker.resources.keys())
            for resource_id in resource_ids:
                self.resource_tracker.unregister_resource(resource_id)


# 装饰器和上下文管理器

def track_resource(resource_type: ResourceType, resource_id: Optional[str] = None,
                  cleanup_callback: Optional[Callable] = None):
    """资源跟踪装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            manager = get_resource_manager()
            actual_resource_id = resource_id or f"{func.__name__}_{id(func)}"
            
            # 注册资源
            manager.register_resource(actual_resource_id, resource_type, cleanup_callback)
            
            try:
                result = func(*args, **kwargs)
                # 更新使用信息
                manager.resource_tracker.update_usage(actual_resource_id)
                return result
            finally:
                # 注销资源
                manager.unregister_resource(actual_resource_id)
        
        return wrapper
    return decorator


@contextmanager
def managed_resource(resource_id: str, resource_type: ResourceType,
                    cleanup_callback: Optional[Callable] = None,
                    metadata: Optional[Dict[str, Any]] = None):
    """资源管理上下文管理器"""
    manager = get_resource_manager()
    
    # 注册资源
    manager.register_resource(resource_id, resource_type, cleanup_callback, metadata)
    
    try:
        yield
        # 更新使用信息
        manager.resource_tracker.update_usage(resource_id)
    finally:
        # 注销资源
        manager.unregister_resource(resource_id)


# 全局资源管理器实例
_resource_manager = None


def get_resource_manager() -> ResourceManager:
    """获取资源管理器实例"""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = ResourceManager()
    return _resource_manager


def cleanup_resources():
    """清理资源（便捷函数）"""
    manager = get_resource_manager()
    manager.resource_tracker.cleanup_unused_resources()
    gc.collect()


def get_memory_usage() -> MemoryStats:
    """获取内存使用情况（便捷函数）"""
    return get_resource_manager().get_memory_stats()


def submit_background_task(fn: Callable, *args, **kwargs) -> Future:
    """提交后台任务（便捷函数）"""
    return get_resource_manager().submit_task(fn, *args, **kwargs)