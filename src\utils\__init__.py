#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础功能模块 (06-基础功能)

本模块提供项目的基础功能支持，包括：
- 异常处理与重试机制
- 任务管理
- 编码处理
- 日志系统
- 配置管理
- 资源管理
"""

# 异常处理
from .exceptions import (
    ScrapingException,
    NetworkException,
    ParseException,
    FileException,
    SystemException,
    RetryManager,
    handle_exception,
    safe_execute
)

# 任务管理
from .task_manager import (
    TaskStatus,
    Task,
    TaskResult,
    TaskManager
)

# 编码处理
from .encoding_utils import (
    EncodingUtils,
    detect_encoding,
    safe_decode,
    safe_encode,
    clean_filename,
    normalize_path,
    normalize_text,
    extract_encoding_from_html
)

# 日志系统
from .logger import (
    LogLevel,
    LoggerManager,
    ColoredFormatter,
    GuiLogHandler,
    setup_logger,
    get_logger,
    set_log_level,
    add_gui_callback,
    remove_gui_callback,
    shutdown_logging
)

# 配置管理
from .config_manager import (
    ConfigFormat,
    ValidationRule,
    ConfigField,
    ConfigSchema,
    ConfigManager,
    create_default_schema,
    get_config_manager,
    get_config,
    set_config,
    load_config_file,
    save_config_file
)

# 资源管理
from .resource_manager import (
    ResourceType,
    ResourceInfo,
    MemoryStats,
    MemoryMonitor,
    ThreadPoolManager,
    ResourceTracker,
    ResourceManager,
    track_resource,
    managed_resource,
    get_resource_manager,
    cleanup_resources,
    get_memory_usage,
    submit_background_task
)

__all__ = [
    # 异常处理
    'ScrapingException', 'NetworkException', 'ParseException', 'FileException', 'SystemException',
    'RetryManager', 'handle_exception', 'safe_execute',
    
    # 任务管理
    'TaskStatus', 'Task', 'TaskResult', 'TaskManager',
    
    # 编码处理
    'EncodingUtils', 'detect_encoding', 'safe_decode', 'safe_encode',
    'clean_filename', 'normalize_path', 'normalize_text', 'extract_encoding_from_html',
    
    # 日志系统
    'LogLevel', 'LoggerManager', 'ColoredFormatter', 'GuiLogHandler',
    'setup_logger', 'get_logger', 'set_log_level',
    'add_gui_callback', 'remove_gui_callback', 'shutdown_logging',
    
    # 配置管理
    'ConfigFormat', 'ValidationRule', 'ConfigField', 'ConfigSchema', 'ConfigManager',
    'create_default_schema', 'get_config_manager', 'get_config', 'set_config',
    'load_config_file', 'save_config_file',
    
    # 资源管理
    'ResourceType', 'ResourceInfo', 'MemoryStats', 'MemoryMonitor', 'ThreadPoolManager',
    'ResourceTracker', 'ResourceManager', 'track_resource', 'managed_resource',
    'get_resource_manager', 'cleanup_resources', 'get_memory_usage', 'submit_background_task'
]