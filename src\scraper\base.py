#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容抓取模块基础类和数据结构

定义了内容抓取的抽象基类、配置类和结果类，为不同框架的内容抓取提供统一接口。

作者: Assistant
创建时间: 2025-07-30
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import time


@dataclass
class ScrapingConfig:
    """抓取配置类"""
    # 请求配置
    timeout: int = 30  # 请求超时时间（秒）
    max_retries: int = 3  # 最大重试次数
    retry_delay: float = 1.0  # 重试延迟（秒）
    delay: float = 1.0  # 请求间隔（秒）
    
    # 内容过滤配置
    remove_navigation: bool = True  # 移除导航元素
    remove_footer: bool = True  # 移除页脚
    remove_ads: bool = True  # 移除广告
    preserve_code_blocks: bool = True  # 保留代码块
    preserve_tables: bool = True  # 保留表格
    min_content_length: int = 100  # 最小内容长度
    
    # Markdown转换配置
    body_width: int = 0  # 文本宽度（0表示不限制）
    unicode_snob: bool = True  # 使用Unicode字符
    escape_all: bool = False  # 转义所有特殊字符
    markdown_options: Dict[str, Any] = field(default_factory=dict)  # 额外的Markdown选项
    
    # 链接和图片处理配置
    process_links: bool = True  # 处理链接
    process_images: bool = True  # 处理图片
    convert_internal_links: bool = True  # 转换内部链接
    base_url: Optional[str] = None  # 基础URL
    
    # 图片处理配置
    download_images: bool = False  # 是否下载图片（第一阶段为False）
    image_dir: Optional[Path] = None  # 图片保存目录
    
    def __post_init__(self):
        """配置验证"""
        if self.timeout <= 0:
            raise ValueError("timeout必须大于0")
        if self.max_retries < 0:
            raise ValueError("max_retries不能小于0")
        if self.retry_delay < 0:
            raise ValueError("retry_delay不能小于0")
        if self.delay < 0:
            raise ValueError("delay不能小于0")
        if self.min_content_length < 0:
            raise ValueError("min_content_length不能小于0")


@dataclass
class ScrapingResult:
    """抓取结果类"""
    url: str  # 原始URL
    title: str  # 页面标题
    content: str  # Markdown内容
    success: bool  # 是否成功
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 统计信息
    content_length: int = 0  # 内容长度
    scraping_time: float = 0.0  # 抓取耗时（秒）
    processing_time: float = 0.0  # 处理耗时（秒）
    
    # 错误信息
    error_message: Optional[str] = None
    error_type: Optional[str] = None
    error: Optional[str] = None  # 兼容性错误字段
    
    # 链接信息
    internal_links: List[str] = field(default_factory=list)  # 内部链接
    external_links: List[str] = field(default_factory=list)  # 外部链接
    images: List[str] = field(default_factory=list)  # 图片链接
    
    def __post_init__(self):
        """计算内容长度"""
        if self.content:
            self.content_length = len(self.content)
    
    def is_valid(self) -> bool:
        """检查结果是否有效"""
        return self.success and bool(self.content.strip())
    
    def get_summary(self) -> str:
        """获取结果摘要"""
        status = "成功" if self.success else "失败"
        summary = f"抓取{status}: {self.title or self.url}"
        if self.success:
            summary += f" (内容长度: {self.content_length}, 耗时: {self.scraping_time:.2f}s)"
        else:
            summary += f" (错误: {self.error_message or self.error or '未知错误'})"
        return summary


class ScrapingError(Exception):
    """抓取异常类"""
    
    def __init__(self, message: str, error_type: str = "unknown", url: str = ""):
        super().__init__(message)
        self.message = message
        self.error_type = error_type
        self.url = url
    
    def __str__(self):
        if self.url:
            return f"[{self.error_type}] {self.message} (URL: {self.url})"
        return f"[{self.error_type}] {self.message}"


class ContentScraper(ABC):
    """内容抓取器抽象基类"""
    
    def __init__(self, name: str):
        self.name = name
        self._default_config = ScrapingConfig()
    
    @abstractmethod
    def scrape(self, url: str, config: Optional[ScrapingConfig] = None) -> ScrapingResult:
        """抓取页面内容
        
        Args:
            url: 页面URL
            config: 抓取配置，如果为None则使用默认配置
            
        Returns:
            ScrapingResult: 抓取结果
        """
        pass
    
    @abstractmethod
    def extract_content(self, html: str, url: str, config: ScrapingConfig) -> str:
        """从HTML中提取主要内容
        
        Args:
            html: HTML内容
            url: 页面URL
            config: 抓取配置
            
        Returns:
            str: 提取的HTML内容
        """
        pass
    
    @abstractmethod
    def convert_to_markdown(self, html: str, config: ScrapingConfig) -> str:
        """将HTML转换为Markdown
        
        Args:
            html: HTML内容
            config: 转换配置
            
        Returns:
            str: Markdown内容
        """
        pass
    
    def get_default_config(self) -> ScrapingConfig:
        """获取默认配置"""
        return self._default_config
    
    def set_default_config(self, config: ScrapingConfig):
        """设置默认配置"""
        self._default_config = config
    
    def __str__(self):
        return f"ContentScraper(name='{self.name}')"
    
    def __repr__(self):
        return self.__str__()