#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Astro+Starlight框架支持模块

本模块实现了对Astro+Starlight文档框架的完整支持，包括框架识别、菜单解析和内容提取功能。
Starlight是基于Astro的现代文档网站框架，广泛用于技术文档和API文档网站。

主要功能:
1. 框架特征检测 - 通过CSS类名、JavaScript对象、Meta标签等特征识别Starlight框架
2. 菜单结构解析 - 递归解析侧边栏导航，支持多层级菜单结构
3. 链接提取与处理 - 提取页面链接并进行URL规范化处理
4. 内容抓取优化 - 针对Starlight特有组件进行内容提取优化

技术特点:
- 支持Starlight v1.x和v2.x版本
- 智能识别侧边栏导航结构
- 保留Starlight特有组件（徽章、提示框等）
- 支持嵌套菜单和动态内容
- 提供详细的日志记录和错误处理

作者: Assistant
创建时间: 2024
"""

import re
import logging
from typing import List, Optional, Dict, Any, Tuple
from urllib.parse import urljoin, urlparse, unquote
from bs4 import BeautifulSoup, Tag
import requests

from .base import (
    DocumentFramework,
    FrameworkDetector,
    MenuParser,
    ContentExtractor,
    PageInfo,
    ContentInfo,
    FrameworkInfo
)


class StarlightDetector(FrameworkDetector):
    """Starlight框架检测器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__ + '.StarlightDetector')
        
        # Starlight框架特征
        self.starlight_features = {
            'css_classes': [
                'astro-',  # Astro框架特征
                'sidebar-sublist-wrapper',  # Starlight侧边栏
                'sl-markdown-content',  # Starlight内容区域
                'starlight-aside',  # Starlight提示框
                'sl-badge',  # Starlight徽章
            ],
            'js_objects': [
                'StarlightThemeProvider',  # Starlight主题提供者
                'starlight',  # Starlight全局对象
            ],
            'meta_tags': [
                'astro-generator',  # Astro生成器标识
                'starlight',  # Starlight标识
            ],
            'dom_selectors': [
                '.sidebar nav',  # 侧边栏导航
                '.sl-nav-wrapper',  # Starlight导航包装器
                'astro-island',  # Astro岛屿组件
            ]
        }
    
    def detect(self, url: str, html_content: str) -> Optional[FrameworkInfo]:
        """检测是否为Starlight框架
        
        Args:
            url: 网站URL
            html_content: HTML内容
            
        Returns:
            Optional[FrameworkInfo]: 框架信息，如果不是Starlight则返回None
        """
        try:
            soup = BeautifulSoup(html_content, 'lxml')
            confidence_score = 0
            detected_features = []
            
            # 检测CSS类名特征
            css_score = self._check_css_features(soup)
            confidence_score += css_score
            if css_score > 0:
                detected_features.append('CSS类名特征')
            
            # 检测JavaScript对象特征
            js_score = self._check_js_features(html_content)
            confidence_score += js_score
            if js_score > 0:
                detected_features.append('JavaScript对象特征')
            
            # 检测Meta标签特征
            meta_score = self._check_meta_features(soup)
            confidence_score += meta_score
            if meta_score > 0:
                detected_features.append('Meta标签特征')
            
            # 检测DOM结构特征
            dom_score = self._check_dom_features(soup)
            confidence_score += dom_score
            if dom_score > 0:
                detected_features.append('DOM结构特征')
            
            # 判断是否为Starlight框架（置信度阈值：30%）
            if confidence_score >= 30:
                self.logger.info(f"检测到Starlight框架，置信度: {confidence_score}%")
                self.logger.info(f"检测到的特征: {', '.join(detected_features)}")
                
                return FrameworkInfo(
                    name="Astro+Starlight",
                    version=self._extract_version(soup),
                    confidence=confidence_score,
                    features=detected_features
                )
            else:
                self.logger.debug(f"未检测到Starlight框架，置信度: {confidence_score}%")
                return None
                
        except Exception as e:
            self.logger.error(f"框架检测失败: {e}")
            return None
    
    def _check_css_features(self, soup: BeautifulSoup) -> int:
        """检测CSS类名特征"""
        score = 0
        
        for feature in self.starlight_features['css_classes']:
            if feature.endswith('-'):
                # 检测以特定前缀开头的类名
                elements = soup.find_all(attrs={'class': re.compile(f'^{feature}')})
            else:
                # 检测包含特定类名的元素
                elements = soup.find_all(class_=re.compile(feature))
            
            if elements:
                score += 15  # 每个特征15分
                self.logger.debug(f"发现CSS特征: {feature}, 元素数量: {len(elements)}")
        
        return min(score, 50)  # CSS特征最高50分
    
    def _check_js_features(self, html_content: str) -> int:
        """检测JavaScript对象特征"""
        score = 0
        
        for feature in self.starlight_features['js_objects']:
            if feature in html_content:
                score += 20  # 每个特征20分
                self.logger.debug(f"发现JavaScript特征: {feature}")
        
        return min(score, 40)  # JS特征最高40分
    
    def _check_meta_features(self, soup: BeautifulSoup) -> int:
        """检测Meta标签特征"""
        score = 0
        
        # 检测generator meta标签
        generator_tag = soup.find('meta', attrs={'name': 'generator'})
        if generator_tag and generator_tag.get('content'):
            content = generator_tag.get('content').lower()
            if 'astro' in content:
                score += 25
                self.logger.debug(f"发现Astro生成器标识: {content}")
        
        # 检测其他meta标签
        for feature in self.starlight_features['meta_tags']:
            if feature == 'astro-generator':
                continue  # 已在上面检测
            
            meta_tag = soup.find('meta', attrs={'name': feature})
            if meta_tag:
                score += 15
                self.logger.debug(f"发现Meta特征: {feature}")
        
        return min(score, 30)  # Meta特征最高30分
    
    def _check_dom_features(self, soup: BeautifulSoup) -> int:
        """检测DOM结构特征"""
        score = 0
        
        for selector in self.starlight_features['dom_selectors']:
            elements = soup.select(selector)
            if elements:
                score += 10  # 每个特征10分
                self.logger.debug(f"发现DOM特征: {selector}, 元素数量: {len(elements)}")
        
        return min(score, 30)  # DOM特征最高30分
    
    def _extract_version(self, soup: BeautifulSoup) -> str:
        """提取框架版本信息"""
        try:
            # 尝试从generator meta标签提取版本
            generator_tag = soup.find('meta', attrs={'name': 'generator'})
            if generator_tag and generator_tag.get('content'):
                content = generator_tag.get('content')
                # 使用正则表达式提取版本号
                version_match = re.search(r'astro[\s@]([\d\.]+)', content, re.IGNORECASE)
                if version_match:
                    return version_match.group(1)
            
            return "unknown"
        except Exception:
            return "unknown"
    
    def get_confidence_score(self, url: str, html_content: str) -> float:
        """获取检测置信度分数
        
        Args:
            url: 网站URL
            html_content: HTML内容
            
        Returns:
            float: 置信度分数（0.0-1.0）
        """
        framework_info = self.detect(url, html_content)
        if framework_info:
            return framework_info.confidence / 100.0
        return 0.0


class StarlightMenuParser(MenuParser):
    """Starlight菜单解析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__ + '.StarlightMenuParser')
        self.max_depth = 5  # 最大菜单深度
    
    def parse_menu(self, url: str, html_content: str) -> List[PageInfo]:
        """解析Starlight菜单结构
        
        Args:
            url: 网站URL
            html_content: HTML内容
            
        Returns:
            List[PageInfo]: 页面信息列表
        """
        try:
            soup = BeautifulSoup(html_content, 'lxml')
            pages = []
            
            # 查找侧边栏导航
            sidebar = self._find_sidebar(soup)
            if not sidebar:
                self.logger.warning("未找到侧边栏")
                return []
            
            # 递归解析菜单结构
            self._parse_menu_recursive(sidebar, url, pages)
            
            return pages
            
        except Exception as e:
            self.logger.error(f"解析菜单结构失败: {e}")
            return []
    
    def _parse_menu_recursive(self, element: Tag, base_url: str, pages: List[PageInfo], 
                            depth: int = 0, parent_path: str = "") -> None:
        """递归解析菜单结构
        
        Args:
            element: 当前解析的DOM元素
            base_url: 基础URL
            pages: 页面信息列表
            depth: 当前深度
            parent_path: 父级路径
        """
        if depth > self.max_depth:
            self.logger.warning(f"菜单深度超过限制 ({self.max_depth})，停止解析")
            return
        
        # 查找所有链接元素
        links = element.find_all('a', href=True)
        
        for link in links:
            try:
                href = link.get('href')
                title = self._extract_link_title(link)
                
                if not href or not title:
                    continue
                
                # URL规范化
                full_url = self._normalize_url(base_url, href)
                if not self._is_valid_page_url(full_url, base_url):
                    continue
                
                # 检测当前页面
                is_current = self._is_current_page(link)
                
                # 计算层级路径
                current_path = f"{parent_path}/{title}" if parent_path else title
                
                # 创建页面信息
                page_info = PageInfo(
                    url=full_url,
                    title=title,
                    level=depth,
                    parent_path=parent_path,
                    is_current=is_current,
                    order=len(pages)
                )
                
                pages.append(page_info)
                self.logger.debug(f"添加页面: {title} ({full_url}) [深度: {depth}]")
                
            except Exception as e:
                self.logger.warning(f"解析链接失败: {e}")
                continue
        
        # 查找子菜单
        sub_menus = element.find_all(['ul', 'details'])
        for sub_menu in sub_menus:
            if sub_menu.parent == element:  # 确保是直接子元素
                self._parse_menu_recursive(sub_menu, base_url, pages, depth + 1, parent_path)
    
    def _extract_link_title(self, link: Tag) -> str:
        """提取链接标题"""
        # 优先使用aria-label
        aria_label = link.get('aria-label')
        if aria_label:
            return aria_label.strip()
        
        # 使用链接文本内容
        text = link.get_text(strip=True)
        if text:
            return text
        
        # 使用title属性
        title = link.get('title')
        if title:
            return title.strip()
        
        # 从href提取
        href = link.get('href', '')
        if href:
            # 提取文件名作为标题
            path_parts = href.strip('/').split('/')
            if path_parts:
                filename = path_parts[-1]
                # 移除文件扩展名
                if '.' in filename:
                    filename = filename.rsplit('.', 1)[0]
                return filename.replace('-', ' ').replace('_', ' ').title()
        
        return "未知页面"
    
    def _normalize_url(self, base_url: str, href: str) -> str:
        """URL规范化
        
        Args:
            base_url: 基础URL
            href: 相对或绝对URL
            
        Returns:
            str: 规范化后的完整URL
        """
        try:
            # 移除锚点
            if '#' in href:
                href = href.split('#')[0]
            
            # 处理空链接
            if not href or href == '/':
                return base_url
            
            # 生成完整URL
            full_url = urljoin(base_url, href)
            
            # URL解码
            full_url = unquote(full_url)
            
            # 确保以/结尾（对于目录）
            if not full_url.endswith('/') and '.' not in full_url.split('/')[-1]:
                full_url += '/'
            
            return full_url
            
        except Exception as e:
            self.logger.warning(f"URL规范化失败: {href} -> {e}")
            return urljoin(base_url, href)
    
    def _is_valid_page_url(self, url: str, base_url: str) -> bool:
        """检查是否为有效的页面URL
        
        Args:
            url: 待检查的URL
            base_url: 基础URL
            
        Returns:
            bool: 是否为有效页面URL
        """
        try:
            parsed_url = urlparse(url)
            parsed_base = urlparse(base_url)
            
            # 必须是同域名
            if parsed_url.netloc != parsed_base.netloc:
                return False
            
            # 过滤特殊路径
            path = parsed_url.path.lower()
            invalid_patterns = [
                '/api/',
                '/assets/',
                '/static/',
                '/_astro/',
                '/public/',
                '.css',
                '.js',
                '.png',
                '.jpg',
                '.jpeg',
                '.gif',
                '.svg',
                '.ico',
                '.pdf',
                '.zip',
                '.tar',
                '.gz'
            ]
            
            for pattern in invalid_patterns:
                if pattern in path:
                    return False
            
            # 必须有路径内容
            if not path or path == '/':
                return True  # 首页是有效的
            
            return True
            
        except Exception:
            return False
    
    def _is_current_page(self, link: Tag) -> bool:
        """检查是否为当前页面
        
        Args:
            link: 链接元素
            
        Returns:
            bool: 是否为当前页面
        """
        # 检查aria-current属性
        aria_current = link.get('aria-current')
        if aria_current == 'page':
            return True
        
        # 检查CSS类名
        classes = link.get('class', [])
        if isinstance(classes, str):
            classes = classes.split()
        
        current_classes = ['current', 'active', 'selected']
        for cls in classes:
            if cls.lower() in current_classes:
                return True
        
        return False
    
    def _deduplicate_pages(self, pages: List[PageInfo]) -> List[PageInfo]:
        """去重页面列表
        
        Args:
            pages: 原始页面列表
            
        Returns:
            List[PageInfo]: 去重后的页面列表
        """
        seen_urls = set()
        unique_pages = []
        
        for page in pages:
            if page.url not in seen_urls:
                seen_urls.add(page.url)
                unique_pages.append(page)
            else:
                self.logger.debug(f"跳过重复页面: {page.title} ({page.url})")
        
        return unique_pages
    
    def _sort_pages(self, pages: List[PageInfo]) -> List[PageInfo]:
        """排序页面列表
        
        Args:
            pages: 原始页面列表
            
        Returns:
            List[PageInfo]: 排序后的页面列表
        """
        # 按层级和顺序排序
        return sorted(pages, key=lambda p: (p.level, p.order))


    def _find_sidebar(self, soup: BeautifulSoup) -> Optional[Tag]:
        """查找侧边栏元素"""
        # 尝试多种选择器查找侧边栏
        selectors = [
            '.sidebar nav',
            '.sl-nav-wrapper',
            '.sidebar-sublist-wrapper',
            'nav[aria-label="Main"]',
            '.sidebar',
            'aside nav'
        ]
        
        for selector in selectors:
            sidebar = soup.select_one(selector)
            if sidebar:
                self.logger.debug(f"使用选择器找到侧边栏: {selector}")
                return sidebar
        
        return None


class StarlightContentExtractor(ContentExtractor):
    """Starlight内容提取器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__ + '.StarlightContentExtractor')
        
        # Starlight内容选择器（按优先级排序）
        self.content_selectors = [
            '.sl-markdown-content',  # Starlight主内容区域
            'main article',  # 主文章内容
            'main .content',  # 主内容区域
            'main',  # 主区域
            'article',  # 文章内容
            '.content',  # 通用内容区域
        ]
        
        # 需要移除的元素选择器
        self.remove_selectors = [
            '.header',  # 页头
            '.sidebar',  # 侧边栏
            '.pagination',  # 分页
            '.edit-page',  # 编辑页面链接
            '.breadcrumb',  # 面包屑导航
            'nav',  # 导航
            '.toc',  # 目录
            '.footer',  # 页脚
            '.sl-nav-wrapper',  # Starlight导航包装器
            'astro-island[data-astro-component-name="Header"]',  # Astro头部组件
            'astro-island[data-astro-component-name="Sidebar"]',  # Astro侧边栏组件
        ]
        
        # 保留的Starlight组件
        self.preserve_selectors = [
            '.sl-badge',  # Starlight徽章
            '.aside',  # 提示框
            '.steps',  # 步骤
            '.expressive-code',  # 代码块
            'pre code',  # 代码块
            '.callout',  # 标注
        ]
    
    def extract_content(self, url: str, html_content: str) -> Optional[ContentInfo]:
        """提取页面内容
        
        Args:
            url: 页面URL
            html_content: HTML内容
            
        Returns:
            Optional[ContentInfo]: 内容信息，提取失败返回None
        """
        try:
            soup = BeautifulSoup(html_content, 'lxml')
            
            # 查找主内容区域
            content_element = self._find_main_content(soup)
            if not content_element:
                self.logger.warning(f"未找到主内容区域: {url}")
                return None
            
            # 清理内容
            cleaned_content = self._clean_content(content_element)
            
            # 提取标题
            title = self._extract_title(soup)
            
            # 提取元数据
            metadata = self._extract_metadata(soup)
            
            # 创建内容信息
            content_info = ContentInfo(
                title=title,
                content=str(cleaned_content),
                metadata=metadata,
                url=url
            )
            
            self.logger.debug(f"内容提取成功: {title} ({len(content_info.content)} 字符)")
            return content_info
            
        except Exception as e:
            self.logger.error(f"内容提取失败 {url}: {e}")
            return None
    
    def _find_main_content(self, soup: BeautifulSoup) -> Optional[Tag]:
        """查找主内容区域"""
        for selector in self.content_selectors:
            element = soup.select_one(selector)
            if element:
                self.logger.debug(f"使用选择器找到内容区域: {selector}")
                return element
        
        self.logger.warning("未找到主内容区域")
        return None
    
    def _clean_content(self, content: Tag) -> Tag:
        """清理内容
        
        Args:
            content: 原始内容元素
            
        Returns:
            Tag: 清理后的内容元素
        """
        # 创建内容副本
        cleaned = BeautifulSoup(str(content), 'lxml')
        
        # 移除不需要的元素
        for selector in self.remove_selectors:
            for element in cleaned.select(selector):
                element.decompose()
        
        # 清理空白和格式
        self._clean_whitespace(cleaned)
        
        return cleaned.body or cleaned
    
    def _clean_whitespace(self, soup: BeautifulSoup) -> None:
        """清理多余的空白字符"""
        # 移除空的段落和div
        for tag in soup.find_all(['p', 'div']):
            if not tag.get_text(strip=True) and not tag.find(['img', 'video', 'audio']):
                tag.decompose()
        
        # 清理连续的换行
        for text_node in soup.find_all(text=True):
            if text_node.parent.name not in ['pre', 'code']:
                cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', str(text_node))
                text_node.replace_with(cleaned_text)
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取页面标题"""
        # 尝试多种方式提取标题
        title_selectors = [
            'h1',  # 主标题
            '.sl-markdown-content h1',  # Starlight主标题
            'title',  # 页面标题
            '.page-title',  # 页面标题类
        ]
        
        for selector in title_selectors:
            element = soup.select_one(selector)
            if element:
                title = element.get_text(strip=True)
                if title:
                    return title
        
        return "未知标题"
    
    def _extract_metadata(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """提取页面元数据"""
        metadata = {}
        
        # 提取描述
        desc_tag = soup.find('meta', attrs={'name': 'description'})
        if desc_tag and desc_tag.get('content'):
            metadata['description'] = desc_tag.get('content')
        
        # 提取关键词
        keywords_tag = soup.find('meta', attrs={'name': 'keywords'})
        if keywords_tag and keywords_tag.get('content'):
            metadata['keywords'] = keywords_tag.get('content')
        
        # 提取作者
        author_tag = soup.find('meta', attrs={'name': 'author'})
        if author_tag and author_tag.get('content'):
            metadata['author'] = author_tag.get('content')
        
        # 提取生成器信息
        generator_tag = soup.find('meta', attrs={'name': 'generator'})
        if generator_tag and generator_tag.get('content'):
            metadata['generator'] = generator_tag.get('content')
        
        return metadata


class StarlightFramework(DocumentFramework):
    """Astro+Starlight框架实现"""
    
    def __init__(self):
        super().__init__("Astro+Starlight")
        
        # 初始化组件
        self._detector = StarlightDetector()
        self._menu_parser = StarlightMenuParser()
        self._content_extractor = StarlightContentExtractor()
    
    def get_detector(self) -> FrameworkDetector:
        """获取框架检测器"""
        return self._detector
    
    def get_menu_parser(self) -> MenuParser:
        """获取菜单解析器"""
        return self._menu_parser
    
    def get_content_extractor(self) -> ContentExtractor:
        """获取内容提取器"""
        return self._content_extractor
    
    def parse_menu_structure(self, url: str, html_content: str) -> List[PageInfo]:
        """解析菜单结构"""
        return self._menu_parser.parse_menu(url, html_content)
    
    def extract_page_content(self, url: str, html_content: str) -> Optional[ContentInfo]:
        """提取页面内容"""
        return self._content_extractor.extract_content(url, html_content)


# 框架工厂函数
def create_starlight_framework() -> StarlightFramework:
    """创建Starlight框架实例
    
    Returns:
        StarlightFramework: Starlight框架实例
    """
    return StarlightFramework()


class StarlightContentExtractor(ContentExtractor):
    """Starlight内容提取器"""
    
    def __init__(self):
        """初始化内容提取器"""
        self.logger = logging.getLogger(__name__ + '.StarlightContentExtractor')
        
        # Starlight内容选择器（按优先级排序）
        self.content_selectors = [
            '.sl-markdown-content',  # Starlight主内容区域
            'main article',  # 主文章内容
            'main .content',  # 主内容区域
            'main',  # 主区域
            'article',  # 文章内容
            '.content',  # 通用内容区域
        ]
        
        # 需要移除的元素选择器
        self.remove_selectors = [
            '.header',  # 页头
            '.sidebar',  # 侧边栏
            '.pagination',  # 分页
            '.edit-page',  # 编辑页面链接
            '.breadcrumb',  # 面包屑导航
            'nav',  # 导航
            '.toc',  # 目录
            '.footer',  # 页脚
            '.sl-nav-wrapper',  # Starlight导航包装器
            'astro-island[data-astro-component-name="Header"]',  # Astro头部组件
            'astro-island[data-astro-component-name="Sidebar"]',  # Astro侧边栏组件
        ]
        
        # 保留的Starlight组件
        self.preserve_selectors = [
            '.sl-badge',  # Starlight徽章
            '.aside',  # 提示框
            '.steps',  # 步骤
            '.expressive-code',  # 代码块
            'pre code',  # 代码块
            '.callout',  # 标注
        ]
    
    def extract_content(self, url: str, html_content: str) -> Optional[ContentInfo]:
        """提取页面内容
        
        Args:
            url: 页面URL
            html_content: HTML内容
            
        Returns:
            Optional[ContentInfo]: 内容信息，提取失败返回None
        """
        try:
            soup = BeautifulSoup(html_content, 'lxml')
            
            # 查找主内容区域
            content_element = self._find_main_content(soup)
            if not content_element:
                self.logger.warning(f"未找到主内容区域: {url}")
                return None
            
            # 清理内容
            cleaned_content = self._clean_content(content_element)
            
            # 提取标题
            title = self._extract_title(soup)
            
            # 提取元数据
            metadata = self._extract_metadata(soup)
            
            # 创建内容信息
            content_info = ContentInfo(
                title=title,
                content=str(cleaned_content),
                metadata=metadata,
                url=url
            )
            
            self.logger.debug(f"内容提取成功: {title} ({len(content_info.content)} 字符)")
            return content_info
            
        except Exception as e:
            self.logger.error(f"内容提取失败 {url}: {e}")
            return None
    
    def _find_main_content(self, soup: BeautifulSoup) -> Optional[Tag]:
        """查找主内容区域"""
        for selector in self.content_selectors:
            element = soup.select_one(selector)
            if element:
                self.logger.debug(f"使用选择器找到内容区域: {selector}")
                return element
        
        self.logger.warning("未找到主内容区域")
        return None
    
    def _clean_content(self, content: Tag) -> Tag:
        """清理内容
        
        Args:
            content: 原始内容元素
            
        Returns:
            Tag: 清理后的内容元素
        """
        # 创建内容副本
        cleaned = BeautifulSoup(str(content), 'lxml')
        
        # 移除不需要的元素
        for selector in self.remove_selectors:
            for element in cleaned.select(selector):
                element.decompose()
        
        # 清理空白和格式
        self._clean_whitespace(cleaned)
        
        return cleaned.body or cleaned
    
    def _clean_whitespace(self, soup: BeautifulSoup) -> None:
        """清理多余的空白字符"""
        # 移除空的段落和div
        for tag in soup.find_all(['p', 'div']):
            if not tag.get_text(strip=True) and not tag.find(['img', 'video', 'audio']):
                tag.decompose()
        
        # 清理连续的换行
        for text_node in soup.find_all(text=True):
            if text_node.parent.name not in ['pre', 'code']:
                cleaned_text = re.sub(r'\n\s*\n\s*\n+', '\n\n', str(text_node))
                text_node.replace_with(cleaned_text)
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取页面标题"""
        # 尝试多种方式提取标题
        title_selectors = [
            'h1',  # 主标题
            '.sl-markdown-content h1',  # Starlight主标题
            'title',  # 页面标题
            '.page-title',  # 页面标题类
        ]
        
        for selector in title_selectors:
            element = soup.select_one(selector)
            if element:
                title = element.get_text(strip=True)
                if title:
                    return title
        
        return "未知标题"
    
    def _extract_metadata(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """提取页面元数据"""
        metadata = {}
        
        # 提取描述
        desc_tag = soup.find('meta', attrs={'name': 'description'})
        if desc_tag and desc_tag.get('content'):
            metadata['description'] = desc_tag.get('content')
        
        # 提取关键词
        keywords_tag = soup.find('meta', attrs={'name': 'keywords'})
        if keywords_tag and keywords_tag.get('content'):
            metadata['keywords'] = keywords_tag.get('content')
        
        # 提取作者
        author_tag = soup.find('meta', attrs={'name': 'author'})
        if author_tag and author_tag.get('content'):
            metadata['author'] = author_tag.get('content')
        
        # 提取生成器信息
        generator_tag = soup.find('meta', attrs={'name': 'generator'})
        if generator_tag and generator_tag.get('content'):
            metadata['generator'] = generator_tag.get('content')
        
        return metadata


class StarlightFramework(DocumentFramework):
    """Astro+Starlight框架实现"""
    
    def __init__(self):
        super().__init__("Astro+Starlight")
        
        # 初始化组件
        self._detector = StarlightDetector()
        self._menu_parser = StarlightMenuParser()
        self._content_extractor = StarlightContentExtractor()
    
    def get_detector(self) -> FrameworkDetector:
        """获取框架检测器"""
        return self._detector
    
    def get_menu_parser(self) -> MenuParser:
        """获取菜单解析器"""
        return self._menu_parser
    
    def get_content_extractor(self) -> ContentExtractor:
        """获取内容提取器"""
        return self._content_extractor
    
    def parse_menu_structure(self, url: str, html_content: str) -> List[PageInfo]:
        """解析菜单结构"""
        return self._menu_parser.parse_menu(url, html_content)
    
    def extract_page_content(self, url: str, html_content: str) -> Optional[ContentInfo]:
        """提取页面内容"""
        return self._content_extractor.extract_content(url, html_content)


# 框架工厂函数
def create_starlight_framework() -> StarlightFramework:
    """创建Starlight框架实例
    
    Returns:
        StarlightFramework: Starlight框架实例
    """
    return StarlightFramework()


# 插件入口点
__all__ = ['StarlightFramework', 'create_starlight_framework']