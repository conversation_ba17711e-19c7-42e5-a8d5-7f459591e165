# 技术文档网站转Markdown工具 - 依赖包列表
# Python 3.8+ 兼容

# 核心GUI框架
# tkinter 是Python标准库，无需安装

# 网络请求库
requests>=2.28.0
urllib3>=1.26.0

# HTML解析库
beautifulsoup4>=4.11.0
lxml>=4.9.0
html5lib>=1.1

# HTML转Markdown
html2text>=2020.1.16
markdownify>=0.11.0

# 编码检测
chardet>=5.0.0

# 系统监控
psutil>=5.9.0

# 配置文件处理
PyYAML>=6.0

# 文件路径处理
# pathlib 是Python 3.4+标准库，无需安装

# 多线程和并发
# threading 是Python标准库，无需安装
# concurrent.futures 是Python 3.2+标准库，无需安装

# 数据处理
# json 是Python标准库，无需安装
# dataclasses 是Python 3.7+标准库，无需安装

# 日志记录
# logging 是Python标准库，无需安装

# 正则表达式
# re 是Python标准库，无需安装

# URL处理
# urllib.parse 是Python标准库，无需安装

# 时间处理
# time 是Python标准库，无需安装
# datetime 是Python标准库，无需安装

# 类型提示
# typing 是Python 3.5+标准库，无需安装

# 枚举类型
# enum 是Python 3.4+标准库，无需安装

# 开发和测试依赖（可选）
# pytest>=7.0.0
# pytest-cov>=4.0.0
# black>=22.0.0
# flake8>=5.0.0
# mypy>=0.991

# GUI主题增强（可选）
# ttkthemes>=3.2.0
# tkinter-tooltip>=2.0.0

# 图像处理（可选，用于处理网站图片）
# Pillow>=9.0.0

# 进度条增强（可选）
# tqdm>=4.64.0

# 配置文件处理增强（可选）
# pyyaml>=6.0
# toml>=0.10.0

# 网络请求增强（可选）
# httpx>=0.24.0  # 现代HTTP客户端，支持异步
# aiohttp>=3.8.0  # 异步HTTP客户端

# 文本处理增强（可选）
# chardet>=5.0.0  # 字符编码检测
# python-magic>=0.4.0  # 文件类型检测