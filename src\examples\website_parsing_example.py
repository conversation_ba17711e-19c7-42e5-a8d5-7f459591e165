#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
02-网站识别与解析模块使用示例

本示例展示如何使用02模块来识别和解析Starlight框架的技术文档网站。
演示了框架检测、菜单解析和内容提取的完整流程。

作者: Assistant
创建时间: 2024
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入02模块的核心功能
from frameworks import (
    get_framework_manager,
    detect_framework,
    parse_menu_structure,
    extract_page_content
)

def example_framework_detection():
    """示例1: 框架检测功能"""
    print("=== 示例1: 框架检测功能 ===")
    
    # 模拟Starlight网站的HTML内容
    starlight_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta name="generator" content="Astro v4.0.0">
        <title>Starlight文档</title>
        <link rel="stylesheet" href="/_astro/starlight.css">
    </head>
    <body>
        <div class="sl-nav-wrapper">
            <nav class="sidebar">
                <div class="sl-sidebar-content">
                    <ul>
                        <li><a href="/getting-started">快速开始</a></li>
                        <li><a href="/guides">使用指南</a></li>
                        <li><a href="/reference">API参考</a></li>
                    </ul>
                </div>
            </nav>
        </div>
        <main>
            <div class="sl-markdown-content">
                <h1>欢迎使用Starlight</h1>
                <p>这是一个现代化的文档网站框架。</p>
            </div>
        </main>
        <script>
            window.StarlightThemeProvider = {
                theme: 'auto'
            };
        </script>
    </body>
    </html>
    """
    
    test_url = "https://starlight.astro.build/getting-started/"
    
    # 检测框架
    framework_name = detect_framework(test_url, starlight_html)
    
    if framework_name:
        print(f"✓ 检测到框架: {framework_name}")
        
        # 获取详细信息需要使用框架管理器
        manager = get_framework_manager()
        results = manager.detect_framework_with_confidence(test_url, starlight_html)
        if results:
            result = results[0]  # 取置信度最高的结果
            print(f"✓ 检测置信度: {result['confidence']:.2f}")
            
            # 获取框架实例来获取更多详细信息
            framework = manager.get_framework(framework_name)
            if framework:
                detector = framework.get_detector()
                detection_info = detector.detect(test_url, starlight_html)
                if detection_info:
                    print(f"✓ 框架版本: {detection_info.version or '未知'}")
                    print(f"✓ 检测特征: {', '.join(detection_info.features)}")
    else:
        print("✗ 未检测到支持的框架")
    
    print()

def example_menu_parsing():
    """示例2: 菜单解析功能"""
    print("=== 示例2: 菜单解析功能 ===")
    
    # 模拟包含复杂菜单结构的HTML
    menu_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta name="generator" content="Astro v4.0.0">
        <title>文档菜单示例</title>
    </head>
    <body>
        <div class="sl-nav-wrapper">
            <nav class="sidebar">
                <div class="sl-sidebar-content">
                    <ul>
                        <li><a href="/" aria-current="page">首页</a></li>
                        <li>
                            <details open>
                                <summary>快速开始</summary>
                                <ul>
                                    <li><a href="/getting-started/installation">安装</a></li>
                                    <li><a href="/getting-started/setup">配置</a></li>
                                    <li><a href="/getting-started/first-project">第一个项目</a></li>
                                </ul>
                            </details>
                        </li>
                        <li>
                            <details>
                                <summary>使用指南</summary>
                                <ul>
                                    <li><a href="/guides/authoring">编写文档</a></li>
                                    <li><a href="/guides/components">组件使用</a></li>
                                    <li>
                                        <details>
                                            <summary>高级功能</summary>
                                            <ul>
                                                <li><a href="/guides/advanced/theming">主题定制</a></li>
                                                <li><a href="/guides/advanced/plugins">插件开发</a></li>
                                            </ul>
                                        </details>
                                    </li>
                                </ul>
                            </details>
                        </li>
                        <li><a href="/api">API参考</a></li>
                    </ul>
                </div>
            </nav>
        </div>
    </body>
    </html>
    """
    
    test_url = "https://example.com/docs"
    
    # 检测框架
    framework_name = detect_framework(test_url, menu_html)
    
    if framework_name:
        # 解析菜单结构
        pages = parse_menu_structure(framework_name, test_url, menu_html)
        
        print(f"✓ 解析到 {len(pages)} 个页面:")
        
        for page in pages:
            indent = "  " * page.level
            current_mark = " (当前页面)" if page.is_current else ""
            print(f"{indent}- {page.title}: {page.url}{current_mark}")
    else:
        print("✗ 无法检测框架，跳过菜单解析")
    
    print()

def example_content_extraction():
    """示例3: 内容提取功能"""
    print("=== 示例3: 内容提取功能 ===")
    
    # 模拟包含丰富内容的页面HTML
    content_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta name="generator" content="Astro v4.0.0">
        <meta name="description" content="这是一个关于Starlight框架的详细指南">
        <meta name="author" content="Starlight团队">
        <title>Starlight框架指南</title>
    </head>
    <body>
        <div class="sl-nav-wrapper">
            <nav class="sidebar"><!-- 侧边栏内容 --></nav>
        </div>
        <main>
            <div class="sl-markdown-content">
                <h1>Starlight框架指南</h1>
                
                <div class="sl-badge">新功能</div>
                
                <p>Starlight是一个基于Astro构建的现代化文档网站框架，专为技术文档而设计。</p>
                
                <h2>主要特性</h2>
                <ul>
                    <li>快速的静态站点生成</li>
                    <li>响应式设计</li>
                    <li>内置搜索功能</li>
                    <li>多语言支持</li>
                </ul>
                
                <div class="aside">
                    <p><strong>提示:</strong> 确保你已经安装了Node.js 18或更高版本。</p>
                </div>
                
                <h2>安装步骤</h2>
                <div class="expressive-code">
                    <pre><code class="language-bash">npm create astro@latest -- --template starlight</code></pre>
                </div>
                
                <p>安装完成后，你就可以开始创建你的文档网站了。</p>
            </div>
        </main>
        <footer class="footer"><!-- 页脚内容 --></footer>
    </body>
    </html>
    """
    
    test_url = "https://starlight.astro.build/guides/getting-started/"
    
    # 检测框架
    framework_name = detect_framework(test_url, content_html)
    
    if framework_name:
        # 提取页面内容
        content = extract_page_content(framework_name, test_url, content_html)
        
        if content:
            print(f"✓ 页面标题: {content.title}")
            print(f"✓ 内容长度: {content.get_content_length()} 字符")
            print(f"✓ URL: {content.url}")
            
            # 显示元数据
            if content.metadata:
                print("✓ 页面元数据:")
                for key, value in content.metadata.items():
                    print(f"  - {key}: {value}")
            
            # 显示内容预览（前200字符）
            preview = content.content[:200].replace('\n', ' ').strip()
            if len(content.content) > 200:
                preview += "..."
            print(f"✓ 内容预览: {preview}")
        else:
            print("✗ 内容提取失败")
    else:
        print("✗ 无法检测框架，跳过内容提取")
    
    print()

def example_framework_manager():
    """示例4: 框架管理器使用"""
    print("=== 示例4: 框架管理器使用 ===")
    
    # 获取框架管理器
    manager = get_framework_manager()
    
    print(f"✓ 框架管理器: {manager}")
    
    # 查看已注册的框架
    frameworks = manager.get_registered_frameworks()
    print(f"✓ 已注册框架: {frameworks}")
    
    # 获取特定框架的信息
    for framework_name in frameworks:
        info = manager.get_framework_info(framework_name)
        if info:
            print(f"✓ {framework_name}框架信息:")
            print(f"  - 类名: {info['class']}")
            print(f"  - 框架名称: {info['framework_name']}")
            print(f"  - 实例状态: {'已创建' if info['instance_created'] else '未创建'}")
    
    print()

def main():
    """主函数 - 运行所有示例"""
    print("02-网站识别与解析模块使用示例")
    print("=" * 50)
    print()
    
    try:
        # 运行各个示例
        example_framework_detection()
        example_menu_parsing()
        example_content_extraction()
        example_framework_manager()
        
        print("=" * 50)
        print("🎉 所有示例运行完成！")
        print()
        print("💡 使用提示:")
        print("1. 框架检测: 使用detect_framework()函数自动识别网站框架")
        print("2. 菜单解析: 使用parse_menu_structure()提取网站导航结构")
        print("3. 内容提取: 使用extract_page_content()获取页面主要内容")
        print("4. 框架管理: 使用get_framework_manager()管理多个框架")
        print()
        print("📚 支持的框架: Astro+Starlight (更多框架正在开发中)")
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)