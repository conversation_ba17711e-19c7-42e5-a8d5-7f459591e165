#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
02-网站识别与解析模块 - 框架支持包

本包实现了对不同文档网站框架的识别和解析支持。
第一阶段专门支持Astro+Starlight框架，后续可扩展支持更多框架。

主要功能:
1. 框架自动识别
2. 菜单结构解析
3. 页面内容提取
4. URL规范化处理

支持的框架:
- Astro+Starlight: 现代文档网站框架

作者: Assistant
创建时间: 2024
"""

# 导入核心基类
from .base import (
    PageInfo,
    ContentInfo,
    FrameworkDetector,
    MenuParser,
    ContentExtractor,
    DocumentFramework
)

# 导入具体框架实现
from .starlight import StarlightFramework, create_starlight_framework

# 导入框架管理器
from .manager import (
    FrameworkManager,
    get_framework_manager,
    detect_framework,
    parse_menu_structure,
    extract_page_content
)

# 版本信息
__version__ = '1.0.0'
__author__ = 'Assistant'

# 导出的公共接口
__all__ = [
    # 基础数据类
    'PageInfo',
    'ContentInfo',
    
    # 抽象基类
    'FrameworkDetector',
    'MenuParser', 
    'ContentExtractor',
    'DocumentFramework',
    
    # 具体框架实现
    'StarlightFramework',
    'create_starlight_framework',
    
    # 框架管理器
    'FrameworkManager',
    'get_framework_manager',
    
    # 便捷函数
    'detect_framework',
    'parse_menu_structure',
    'extract_page_content',
]