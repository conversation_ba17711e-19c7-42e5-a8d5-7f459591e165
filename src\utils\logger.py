#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志记录系统模块

本模块提供统一的日志记录功能，支持多级别日志、控制台输出和文件输出。
"""

import os
import sys
import logging
import logging.handlers
from typing import Optional, Dict, Any, TextIO
from enum import Enum
from datetime import datetime
import threading


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
        'RESET': '\033[0m'        # 重置
    }
    
    def __init__(self, fmt: Optional[str] = None, datefmt: Optional[str] = None, 
                 use_colors: bool = True):
        super().__init__(fmt, datefmt)
        self.use_colors = use_colors and self._supports_color()
    
    def _supports_color(self) -> bool:
        """检查终端是否支持颜色"""
        # Windows系统检查
        if os.name == 'nt':
            try:
                import colorama
                colorama.init()
                return True
            except ImportError:
                return False
        
        # Unix系统检查
        return hasattr(sys.stderr, 'isatty') and sys.stderr.isatty()
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        # 获取基础格式化结果
        formatted = super().format(record)
        
        # 添加颜色
        if self.use_colors:
            level_name = record.levelname
            color = self.COLORS.get(level_name, '')
            reset = self.COLORS['RESET']
            
            if color:
                # 只给级别名称添加颜色
                formatted = formatted.replace(
                    f'[{level_name}]',
                    f'[{color}{level_name}{reset}]'
                )
        
        return formatted


class GuiLogHandler(logging.Handler):
    """GUI日志处理器，将日志发送到GUI组件"""
    
    def __init__(self, callback=None):
        super().__init__()
        self.callback = callback
        self.lock = threading.Lock()
    
    def set_callback(self, callback):
        """设置回调函数"""
        with self.lock:
            self.callback = callback
    
    def emit(self, record: logging.LogRecord):
        """发送日志记录"""
        if self.callback:
            try:
                msg = self.format(record)
                with self.lock:
                    if self.callback:
                        self.callback(msg, record.levelname)
            except Exception:
                self.handleError(record)


class LoggerManager:
    """日志管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.loggers: Dict[str, logging.Logger] = {}
        self.gui_handler: Optional[GuiLogHandler] = None
        self.file_handler: Optional[logging.FileHandler] = None
        self.console_handler: Optional[logging.StreamHandler] = None
        
        # 默认配置
        self.default_level = LogLevel.INFO
        self.default_format = '[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s'
        self.default_date_format = '%Y-%m-%d %H:%M:%S'
    
    def setup_logger(self, name: str = 'scraper', level: LogLevel = None, 
                    console_output: bool = True, file_output: Optional[str] = None,
                    gui_callback=None, format_string: Optional[str] = None,
                    date_format: Optional[str] = None) -> logging.Logger:
        """
        设置日志记录器
        
        Args:
            name: 日志记录器名称
            level: 日志级别
            console_output: 是否输出到控制台
            file_output: 文件输出路径
            gui_callback: GUI回调函数
            format_string: 日志格式字符串
            date_format: 日期格式字符串
            
        Returns:
            配置好的日志记录器
        """
        # 获取或创建logger
        logger = logging.getLogger(name)
        
        # 避免重复配置
        if name in self.loggers:
            return self.loggers[name]
        
        # 设置级别
        if level is None:
            level = self.default_level
        logger.setLevel(level.value)
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 格式化器
        fmt = format_string or self.default_format
        datefmt = date_format or self.default_date_format
        
        # 控制台处理器
        if console_output:
            if not self.console_handler:
                self.console_handler = logging.StreamHandler(sys.stdout)
                console_formatter = ColoredFormatter(fmt, datefmt)
                self.console_handler.setFormatter(console_formatter)
            logger.addHandler(self.console_handler)
        
        # 文件处理器
        if file_output:
            try:
                # 确保目录存在
                os.makedirs(os.path.dirname(file_output), exist_ok=True)
                
                # 使用RotatingFileHandler防止文件过大
                file_handler = logging.handlers.RotatingFileHandler(
                    file_output, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
                )
                file_formatter = logging.Formatter(fmt, datefmt)
                file_handler.setFormatter(file_formatter)
                logger.addHandler(file_handler)
                
                self.file_handler = file_handler
            except Exception as e:
                print(f"创建文件日志处理器失败: {e}")
        
        # GUI处理器
        if gui_callback:
            if not self.gui_handler:
                self.gui_handler = GuiLogHandler(gui_callback)
                gui_formatter = logging.Formatter('[%(levelname)s] %(message)s')
                self.gui_handler.setFormatter(gui_formatter)
            else:
                self.gui_handler.set_callback(gui_callback)
            logger.addHandler(self.gui_handler)
        
        # 防止日志传播到根logger
        logger.propagate = False
        
        self.loggers[name] = logger
        return logger
    
    def get_logger(self, name: str = 'scraper') -> logging.Logger:
        """获取日志记录器"""
        if name in self.loggers:
            return self.loggers[name]
        else:
            return self.setup_logger(name)
    
    def set_level(self, level: LogLevel, logger_name: Optional[str] = None):
        """设置日志级别"""
        if logger_name:
            if logger_name in self.loggers:
                self.loggers[logger_name].setLevel(level.value)
        else:
            # 设置所有logger的级别
            for logger in self.loggers.values():
                logger.setLevel(level.value)
    
    def add_gui_callback(self, callback, logger_name: str = 'scraper'):
        """添加GUI回调"""
        if not self.gui_handler:
            self.gui_handler = GuiLogHandler(callback)
            gui_formatter = logging.Formatter('[%(levelname)s] %(message)s')
            self.gui_handler.setFormatter(gui_formatter)
        else:
            self.gui_handler.set_callback(callback)
        
        if logger_name in self.loggers:
            logger = self.loggers[logger_name]
            if self.gui_handler not in logger.handlers:
                logger.addHandler(self.gui_handler)
    
    def remove_gui_callback(self, logger_name: str = 'scraper'):
        """移除GUI回调"""
        if self.gui_handler and logger_name in self.loggers:
            logger = self.loggers[logger_name]
            if self.gui_handler in logger.handlers:
                logger.removeHandler(self.gui_handler)
    
    def shutdown(self):
        """关闭日志系统"""
        for logger in self.loggers.values():
            for handler in logger.handlers[:]:
                handler.close()
                logger.removeHandler(handler)
        
        self.loggers.clear()
        
        if self.file_handler:
            self.file_handler.close()
            self.file_handler = None
        
        if self.console_handler:
            self.console_handler.close()
            self.console_handler = None
        
        if self.gui_handler:
            self.gui_handler.close()
            self.gui_handler = None


# 全局日志管理器实例
_logger_manager = LoggerManager()


def setup_logger(name: str = 'scraper', level: LogLevel = LogLevel.INFO,
                console_output: bool = True, file_output: Optional[str] = None,
                gui_callback=None, format_string: Optional[str] = None,
                date_format: Optional[str] = None) -> logging.Logger:
    """设置日志记录器（便捷函数）"""
    return _logger_manager.setup_logger(
        name, level, console_output, file_output, gui_callback, 
        format_string, date_format
    )


def get_logger(name: str = 'scraper') -> logging.Logger:
    """获取日志记录器（便捷函数）"""
    return _logger_manager.get_logger(name)


def set_log_level(level: LogLevel, logger_name: Optional[str] = None):
    """设置日志级别（便捷函数）"""
    _logger_manager.set_level(level, logger_name)


def add_gui_callback(callback, logger_name: str = 'scraper'):
    """添加GUI回调（便捷函数）"""
    _logger_manager.add_gui_callback(callback, logger_name)


def remove_gui_callback(logger_name: str = 'scraper'):
    """移除GUI回调（便捷函数）"""
    _logger_manager.remove_gui_callback(logger_name)


def shutdown_logging():
    """关闭日志系统（便捷函数）"""
    _logger_manager.shutdown()


# 创建默认logger
default_logger = get_logger()