#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容抓取与处理模块使用示例

展示如何使用模块03的各种功能，包括单页抓取、批量抓取、链接处理和图片处理。

作者: Assistant
创建时间: 2025-07-30
"""

import asyncio
import time
from pathlib import Path
from typing import List

# 导入抓取模块
from src.scraper import (
    ScrapingConfig, ScrapingResult, ScrapingTask,
    StarlightContentScraper, ScrapingManager,
    MarkdownConverter, LinkProcessor, ImageProcessor,
    create_scraper, scrape_content, create_manager
)


def example_1_basic_scraping():
    """
    示例1: 基础内容抓取
    
    演示如何抓取单个页面的内容并转换为Markdown。
    """
    print("\n" + "="*60)
    print("示例1: 基础内容抓取")
    print("="*60)
    
    # 使用便捷函数快速抓取
    print("\n1. 使用便捷函数快速抓取:")
    
    # 模拟抓取（实际使用时替换为真实URL）
    test_url = "https://starlight.astro.build/getting-started/"
    print(f"目标URL: {test_url}")
    
    try:
        # 快速抓取内容
        result = scrape_content(test_url)
        
        if result.success:
            print(f"✓ 抓取成功!")
            print(f"  标题: {result.title}")
            print(f"  内容长度: {len(result.content)} 字符")
            print(f"  处理时间: {result.processing_time:.2f} 秒")
            print(f"  内容预览: {result.content[:200]}...")
        else:
            print(f"✗ 抓取失败: {result.error}")
    
    except Exception as e:
        print(f"✗ 抓取过程中出现错误: {e}")
    
    # 使用自定义配置
    print("\n2. 使用自定义配置:")
    
    config = ScrapingConfig(
        timeout=60,
        max_retries=5,
        process_links=True,
        process_images=True,
        min_content_length=50
    )
    
    scraper = create_scraper(config)
    print(f"配置信息:")
    print(f"  超时时间: {config.timeout} 秒")
    print(f"  最大重试: {config.max_retries} 次")
    print(f"  处理链接: {config.process_links}")
    print(f"  处理图片: {config.process_images}")
    print(f"  最小内容长度: {config.min_content_length} 字符")


def example_2_markdown_conversion():
    """
    示例2: HTML到Markdown转换
    
    演示如何使用MarkdownConverter进行HTML转换。
    """
    print("\n" + "="*60)
    print("示例2: HTML到Markdown转换")
    print("="*60)
    
    # 创建转换器
    config = ScrapingConfig()
    converter = MarkdownConverter(config)
    
    # 测试各种HTML内容
    test_cases = [
        {
            'name': '基本HTML元素',
            'html': '''
            <h1>主标题</h1>
            <h2>副标题</h2>
            <p>这是一个包含 <strong>粗体</strong> 和 <em>斜体</em> 的段落。</p>
            <ul>
                <li>列表项1</li>
                <li>列表项2</li>
            </ul>
            '''
        },
        {
            'name': 'Starlight Aside组件',
            'html': '''
            <div class="sl-aside note">
                <p>这是一个提示信息。</p>
            </div>
            <div class="sl-aside caution">
                <p>这是一个警告信息。</p>
            </div>
            '''
        },
        {
            'name': '代码块',
            'html': '''
            <pre><code class="language-python">
def hello_world():
    print("Hello, World!")
    return True
</code></pre>
            '''
        },
        {
            'name': '表格',
            'html': '''
            <table>
                <thead>
                    <tr><th>功能</th><th>状态</th><th>描述</th></tr>
                </thead>
                <tbody>
                    <tr><td>抓取</td><td>✓</td><td>网页内容抓取</td></tr>
                    <tr><td>转换</td><td>✓</td><td>HTML到Markdown</td></tr>
                    <tr><td>处理</td><td>✓</td><td>链接和图片处理</td></tr>
                </tbody>
            </table>
            '''
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}:")
        print("HTML输入:")
        print(test_case['html'].strip())
        
        markdown = converter.convert(test_case['html'])
        print("\nMarkdown输出:")
        print(markdown)
        print("-" * 40)


def example_3_link_processing():
    """
    示例3: 链接处理
    
    演示如何处理内部链接转换和外部链接保留。
    """
    print("\n" + "="*60)
    print("示例3: 链接处理")
    print("="*60)
    
    # 创建链接处理器
    base_url = "https://docs.example.com"
    processor = LinkProcessor(base_url)
    
    # 测试各种链接类型
    test_markdown = '''
# 文档示例

这里有各种类型的链接：

- [内部链接1](/getting-started/)
- [内部链接2](/api/reference.astro)
- [外部链接](https://github.com/example/repo)
- [锚点链接](#section-title)
- [相对链接](../guide/advanced.md)

还有一些HTML格式的链接：
<a href="/tutorials/basic">基础教程</a>
<a href="https://external.com">外部站点</a>
    '''
    
    print("原始Markdown:")
    print(test_markdown)
    
    # 处理链接
    target_dir = Path("/docs/current")
    processed_markdown, link_stats = processor.process_links(test_markdown, target_dir)
    
    print("\n处理后的Markdown:")
    print(processed_markdown)
    
    print("\n链接统计信息:")
    print(f"  内部链接: {len(link_stats['internal'])} 个")
    for link in link_stats['internal']:
        print(f"    - {link}")
    
    print(f"  外部链接: {len(link_stats['external'])} 个")
    for link in link_stats['external']:
        print(f"    - {link}")
    
    print(f"  锚点链接: {len(link_stats['anchors'])} 个")
    for link in link_stats['anchors']:
        print(f"    - {link}")
    
    print(f"  转换的链接: {len(link_stats['converted'])} 个")
    for conversion in link_stats['converted']:
        print(f"    - {conversion['original']} → {conversion['converted']}")


def example_4_image_processing():
    """
    示例4: 图片处理
    
    演示如何处理图片链接和元数据提取。
    """
    print("\n" + "="*60)
    print("示例4: 图片处理")
    print("="*60)
    
    # 创建图片处理器
    base_url = "https://docs.example.com"
    processor = ImageProcessor(base_url)
    
    # 测试各种图片类型
    test_markdown = '''
# 图片示例

这里有各种类型的图片：

![Logo](/assets/logo.png)
![Diagram](./diagrams/architecture.svg)
![External Image](https://cdn.example.com/banner.jpg)

还有HTML格式的图片：
<img src="/screenshots/interface.png" alt="界面截图" />
<img src="https://external.com/image.gif" alt="动画" />
    '''
    
    print("原始Markdown:")
    print(test_markdown)
    
    # 处理图片
    processed_markdown, image_stats = processor.process_images(test_markdown)
    
    print("\n处理后的Markdown:")
    print(processed_markdown)
    
    print("\n图片统计信息:")
    print(f"  处理的图片: {len(image_stats['processed'])} 个")
    
    print(f"  内部图片: {len(image_stats['internal'])} 个")
    for img in image_stats['internal']:
        print(f"    - {img['original_src']} → {img['absolute_url']}")
    
    print(f"  外部图片: {len(image_stats['external'])} 个")
    for img in image_stats['external']:
        print(f"    - {img['original_src']}")
    
    print(f"  无效图片: {len(image_stats['invalid'])} 个")
    for img in image_stats['invalid']:
        print(f"    - {img['original_src']}")


def example_5_batch_scraping():
    """
    示例5: 批量抓取
    
    演示如何使用ScrapingManager进行批量抓取。
    """
    print("\n" + "="*60)
    print("示例5: 批量抓取")
    print("="*60)
    
    # 创建抓取管理器
    config = ScrapingConfig(
        timeout=30,
        max_retries=2,
        process_links=True,
        process_images=True
    )
    
    manager = create_manager(config, max_workers=3)
    
    # 模拟URL列表（实际使用时替换为真实URL）
    urls = [
        "https://starlight.astro.build/getting-started/",
        "https://starlight.astro.build/guides/authoring-content/",
        "https://starlight.astro.build/guides/components/",
        "https://starlight.astro.build/reference/configuration/"
    ]
    
    print(f"准备抓取 {len(urls)} 个页面:")
    for i, url in enumerate(urls, 1):
        print(f"  {i}. {url}")
    
    # 设置回调函数
    def progress_callback(progress):
        print(f"\r进度: {progress.progress_percentage:.1f}% "
              f"({progress.completed_tasks + progress.failed_tasks}/{progress.total_tasks}) "
              f"当前: {progress.current_task[:50]}...", end="")
    
    def result_callback(result):
        if result.success:
            print(f"\n✓ 成功: {result.url} ({len(result.content)} 字符)")
        else:
            print(f"\n✗ 失败: {result.url} - {result.error}")
    
    manager.set_progress_callback(progress_callback)
    manager.set_result_callback(result_callback)
    
    # 添加任务
    manager.add_urls(urls)
    
    print(f"\n开始批量抓取 (最大 {manager.max_workers} 个并发线程)...")
    start_time = time.time()
    
    try:
        # 执行抓取（这里模拟，实际会进行网络请求）
        results = list(manager.start_scraping())
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n\n抓取完成! 总用时: {total_time:.2f} 秒")
        
        # 获取统计信息
        stats = manager.get_stats()
        print(f"\n统计信息:")
        print(f"  总页面: {stats['total_pages']}")
        print(f"  成功: {stats['successful_pages']}")
        print(f"  失败: {stats['failed_pages']}")
        print(f"  成功率: {stats.get('success_rate', 0):.1f}%")
        print(f"  总内容长度: {stats['total_content_length']} 字符")
        print(f"  平均处理时间: {stats['average_processing_time']:.2f} 秒/页")
        
        # 导出结果摘要
        summary = manager.export_results_summary()
        print(f"\n结果摘要:")
        print(f"  平均内容长度: {summary['content_stats']['average_content_length']:.0f} 字符")
        
        if summary['failed_urls']:
            print(f"  失败的URL:")
            for failed in summary['failed_urls']:
                print(f"    - {failed['url']}: {failed['error']}")
    
    except Exception as e:
        print(f"\n批量抓取过程中出现错误: {e}")


def example_6_advanced_usage():
    """
    示例6: 高级用法
    
    演示高级功能，如自定义处理、错误处理和性能优化。
    """
    print("\n" + "="*60)
    print("示例6: 高级用法")
    print("="*60)
    
    # 1. 自定义抓取配置
    print("\n1. 自定义抓取配置:")
    
    config = ScrapingConfig(
        timeout=45,
        max_retries=3,
        retry_delay=2.0,
        process_links=True,
        process_images=True,
        min_content_length=200,
        # Markdown转换配置
        markdown_options={
            'body_width': 0,  # 不限制行宽
            'protect_links': True,  # 保护链接
            'unicode_snob': True,  # 使用Unicode
        }
    )
    
    print(f"配置详情:")
    print(f"  超时: {config.timeout}s, 重试: {config.max_retries}次")
    print(f"  重试延迟: {config.retry_delay}s")
    print(f"  最小内容长度: {config.min_content_length}字符")
    print(f"  Markdown选项: {config.markdown_options}")
    
    # 2. 错误处理和重试机制
    print("\n2. 错误处理和重试机制:")
    
    def demonstrate_error_handling():
        """演示错误处理"""
        scraper = StarlightContentScraper(config)
        
        # 模拟各种错误情况
        error_urls = [
            "https://nonexistent-domain-12345.com",  # 域名不存在
            "https://httpstat.us/404",  # 404错误
            "https://httpstat.us/500",  # 服务器错误
        ]
        
        for url in error_urls:
            print(f"  测试URL: {url}")
            try:
                result = scraper.scrape(url)
                if result.success:
                    print(f"    ✓ 成功")
                else:
                    print(f"    ✗ 失败: {result.error}")
            except Exception as e:
                print(f"    ✗ 异常: {e}")
    
    # demonstrate_error_handling()  # 注释掉以避免实际网络请求
    print("  (错误处理演示已注释，避免实际网络请求)")
    
    # 3. 性能监控
    print("\n3. 性能监控:")
    
    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.page_times = []
        
        def on_progress(self, progress):
            if self.start_time is None:
                self.start_time = time.time()
            
            elapsed = time.time() - self.start_time
            if progress.total_tasks > 0:
                eta = elapsed / (progress.completed_tasks + progress.failed_tasks) * progress.total_tasks - elapsed if progress.completed_tasks + progress.failed_tasks > 0 else 0
                print(f"    进度: {progress.progress_percentage:.1f}%, ETA: {eta:.1f}s")
        
        def on_result(self, result):
            if result.processing_time:
                self.page_times.append(result.processing_time)
                avg_time = sum(self.page_times) / len(self.page_times)
                print(f"    页面处理时间: {result.processing_time:.2f}s (平均: {avg_time:.2f}s)")
    
    monitor = PerformanceMonitor()
    print("  性能监控器已创建，可用于实时监控抓取性能")
    
    # 4. 内容质量检查
    print("\n4. 内容质量检查:")
    
    def check_content_quality(result: ScrapingResult) -> dict:
        """检查内容质量"""
        quality = {
            'has_title': bool(result.title.strip()),
            'content_length': len(result.content),
            'has_headings': '# ' in result.content or '## ' in result.content,
            'has_links': '[' in result.content and '](' in result.content,
            'has_images': '![' in result.content,
            'estimated_reading_time': len(result.content.split()) / 200  # 假设200词/分钟
        }
        
        # 计算质量分数
        score = 0
        if quality['has_title']: score += 20
        if quality['content_length'] > 500: score += 20
        if quality['has_headings']: score += 20
        if quality['has_links']: score += 20
        if quality['has_images']: score += 20
        
        quality['quality_score'] = score
        return quality
    
    # 示例质量检查
    sample_result = ScrapingResult(
        url="https://example.com",
        title="示例文档",
        content="# 主标题\n\n这是一个包含[链接](https://example.com)和![图片](image.png)的示例文档。\n\n## 子标题\n\n更多内容...",
        metadata={},
        success=True
    )
    
    quality = check_content_quality(sample_result)
    print(f"  内容质量分析:")
    print(f"    标题: {'✓' if quality['has_title'] else '✗'}")
    print(f"    内容长度: {quality['content_length']} 字符")
    print(f"    包含标题: {'✓' if quality['has_headings'] else '✗'}")
    print(f"    包含链接: {'✓' if quality['has_links'] else '✗'}")
    print(f"    包含图片: {'✓' if quality['has_images'] else '✗'}")
    print(f"    预计阅读时间: {quality['estimated_reading_time']:.1f} 分钟")
    print(f"    质量分数: {quality['quality_score']}/100")


def main():
    """
    主函数 - 运行所有示例
    """
    print("内容抓取与处理模块 - 使用示例")
    print("=" * 80)
    print("本示例展示模块03的各种功能和用法")
    print("注意: 某些示例使用模拟数据以避免实际网络请求")
    
    try:
        # 运行所有示例
        example_1_basic_scraping()
        example_2_markdown_conversion()
        example_3_link_processing()
        example_4_image_processing()
        example_5_batch_scraping()
        example_6_advanced_usage()
        
        print("\n" + "="*80)
        print("所有示例运行完成!")
        print("\n实用提示:")
        print("1. 在实际使用中，请替换示例URL为真实的Starlight文档站点")
        print("2. 根据目标网站调整超时和重试配置")
        print("3. 使用批量抓取时注意控制并发数，避免对目标服务器造成压力")
        print("4. 定期检查和更新选择器，以适应网站结构变化")
        print("5. 实施适当的错误处理和日志记录")
        
    except Exception as e:
        print(f"\n运行示例时出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()