#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
框架基类定义模块

本模块定义了文档框架系统的核心抽象基类和数据结构，为不同框架的实现提供统一接口。
包含框架检测器、菜单解析器、内容提取器等核心组件的抽象定义。

主要组件:
- FrameworkDetector: 框架检测器抽象基类
- MenuParser: 菜单解析器抽象基类  
- ContentExtractor: 内容提取器抽象基类
- DocumentFramework: 文档框架抽象基类
- PageInfo: 页面信息数据类
- ContentInfo: 内容信息数据类

作者: Assistant
创建时间: 2024
"""

import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Optional, Dict, Any


@dataclass
class PageInfo:
    """页面信息数据类
    
    用于存储从网站菜单中解析出的页面信息，包括URL、标题、层级等元数据。
    
    Attributes:
        url: 页面完整URL
        title: 页面标题
        level: 页面在菜单中的层级深度（0为根级）
        parent_path: 父级路径（用于构建层级结构）
        is_current: 是否为当前页面
        order: 在同级页面中的排序位置
        metadata: 额外的元数据信息
    """
    url: str
    title: str
    level: int = 0
    parent_path: str = ""
    is_current: bool = False
    order: int = 0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.metadata is None:
            self.metadata = {}
    
    def get_full_path(self) -> str:
        """获取完整路径
        
        Returns:
            str: 完整的层级路径
        """
        if self.parent_path:
            return f"{self.parent_path}/{self.title}"
        return self.title
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"PageInfo(title='{self.title}', url='{self.url}', level={self.level})"


@dataclass
class FrameworkInfo:
    """框架信息数据类
    
    用于存储框架检测结果，包括框架名称、置信度、版本等信息。
    
    Attributes:
        name: 框架名称
        confidence: 检测置信度（0-100）
        version: 框架版本（如果能检测到）
        features: 检测到的特征列表
        metadata: 额外的框架元数据
    """
    name: str
    confidence: float
    version: Optional[str] = None
    features: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.features is None:
            self.features = []
        if self.metadata is None:
            self.metadata = {}
    
    def is_confident(self, threshold: float = 50.0) -> bool:
        """检查置信度是否达到阈值
        
        Args:
            threshold: 置信度阈值
            
        Returns:
            bool: 是否达到阈值
        """
        return self.confidence >= threshold
    
    def add_feature(self, feature: str) -> None:
        """添加检测到的特征
        
        Args:
            feature: 特征名称
        """
        if feature not in self.features:
            self.features.append(feature)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"FrameworkInfo(name='{self.name}', confidence={self.confidence:.1f}%)"


@dataclass
class ContentInfo:
    """内容信息数据类
    
    用于存储从网页中提取的内容信息，包括标题、正文内容、元数据等。
    
    Attributes:
        title: 页面标题
        content: 页面主要内容（HTML格式）
        metadata: 页面元数据（描述、关键词、作者等）
        url: 页面URL
        extracted_at: 提取时间戳
        content_type: 内容类型（如：markdown、html等）
    """
    title: str
    content: str
    metadata: Dict[str, Any]
    url: str
    extracted_at: Optional[str] = None
    content_type: str = "html"
    
    def __post_init__(self):
        """初始化后处理"""
        if self.extracted_at is None:
            from datetime import datetime
            self.extracted_at = datetime.now().isoformat()
    
    def get_content_length(self) -> int:
        """获取内容长度
        
        Returns:
            int: 内容字符数
        """
        return len(self.content)
    
    def has_metadata(self, key: str) -> bool:
        """检查是否包含指定元数据
        
        Args:
            key: 元数据键名
            
        Returns:
            bool: 是否包含该元数据
        """
        return key in self.metadata and self.metadata[key]
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ContentInfo(title='{self.title}', length={self.get_content_length()}, url='{self.url}')"


class FrameworkDetector(ABC):
    """框架检测器抽象基类
    
    定义了框架检测的标准接口，用于识别网站使用的文档框架类型。
    不同框架需要实现自己的检测逻辑。
    """
    
    def __init__(self, framework_name: str):
        """初始化检测器
        
        Args:
            framework_name: 框架名称
        """
        self.framework_name = framework_name
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def detect(self, url: str, html_content: str) -> bool:
        """检测是否为目标框架
        
        Args:
            url: 网站URL
            html_content: HTML内容
            
        Returns:
            bool: 是否为目标框架
        """
        pass
    
    @abstractmethod
    def get_confidence_score(self, url: str, html_content: str) -> float:
        """获取检测置信度分数
        
        Args:
            url: 网站URL
            html_content: HTML内容
            
        Returns:
            float: 置信度分数（0.0-1.0）
        """
        pass
    
    def get_framework_name(self) -> str:
        """获取框架名称
        
        Returns:
            str: 框架名称
        """
        return self.framework_name


class MenuParser(ABC):
    """菜单解析器抽象基类
    
    定义了菜单解析的标准接口，用于从网站中提取导航菜单结构。
    不同框架需要实现自己的菜单解析逻辑。
    """
    
    def __init__(self):
        """初始化解析器"""
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def parse_menu(self, url: str, html_content: str) -> List[PageInfo]:
        """解析菜单结构
        
        Args:
            url: 网站URL
            html_content: HTML内容
            
        Returns:
            List[PageInfo]: 页面信息列表
        """
        pass
    
    def validate_pages(self, pages: List[PageInfo]) -> List[PageInfo]:
        """验证页面列表
        
        Args:
            pages: 原始页面列表
            
        Returns:
            List[PageInfo]: 验证后的页面列表
        """
        valid_pages = []
        for page in pages:
            if self._is_valid_page(page):
                valid_pages.append(page)
            else:
                self.logger.warning(f"跳过无效页面: {page}")
        
        return valid_pages
    
    def _is_valid_page(self, page: PageInfo) -> bool:
        """检查页面是否有效
        
        Args:
            page: 页面信息
            
        Returns:
            bool: 是否有效
        """
        # 基本验证
        if not page.url or not page.title:
            return False
        
        # URL格式验证
        if not page.url.startswith(('http://', 'https://')):
            return False
        
        # 标题长度验证
        if len(page.title.strip()) == 0:
            return False
        
        return True


class ContentExtractor(ABC):
    """内容提取器抽象基类
    
    定义了内容提取的标准接口，用于从网页中提取主要内容。
    不同框架需要实现自己的内容提取逻辑。
    """
    
    def __init__(self):
        """初始化提取器"""
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def extract_content(self, url: str, html_content: str) -> Optional[ContentInfo]:
        """提取页面内容
        
        Args:
            url: 页面URL
            html_content: HTML内容
            
        Returns:
            Optional[ContentInfo]: 内容信息，提取失败返回None
        """
        pass
    
    def validate_content(self, content: ContentInfo) -> bool:
        """验证内容是否有效
        
        Args:
            content: 内容信息
            
        Returns:
            bool: 是否有效
        """
        if not content:
            return False
        
        # 检查必要字段
        if not content.title or not content.content or not content.url:
            return False
        
        # 检查内容长度
        if len(content.content.strip()) < 10:  # 内容太短
            return False
        
        return True


class DocumentFramework(ABC):
    """文档框架抽象基类
    
    定义了文档框架的标准接口，整合框架检测、菜单解析和内容提取功能。
    每个具体框架需要继承此类并实现相应方法。
    """
    
    def __init__(self, name: str):
        """初始化框架
        
        Args:
            name: 框架名称
        """
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def get_detector(self) -> FrameworkDetector:
        """获取框架检测器
        
        Returns:
            FrameworkDetector: 框架检测器实例
        """
        pass
    
    @abstractmethod
    def get_menu_parser(self) -> MenuParser:
        """获取菜单解析器
        
        Returns:
            MenuParser: 菜单解析器实例
        """
        pass
    
    @abstractmethod
    def get_content_extractor(self) -> ContentExtractor:
        """获取内容提取器
        
        Returns:
            ContentExtractor: 内容提取器实例
        """
        pass
    
    def detect_framework(self, url: str, html_content: str) -> bool:
        """检测是否为当前框架
        
        Args:
            url: 网站URL
            html_content: HTML内容
            
        Returns:
            bool: 是否为当前框架
        """
        detector = self.get_detector()
        return detector.detect(url, html_content)
    
    def parse_menu_structure(self, url: str, html_content: str) -> List[PageInfo]:
        """解析菜单结构
        
        Args:
            url: 网站URL
            html_content: HTML内容
            
        Returns:
            List[PageInfo]: 页面信息列表
        """
        parser = self.get_menu_parser()
        pages = parser.parse_menu(url, html_content)
        return parser.validate_pages(pages)
    
    def extract_page_content(self, url: str, html_content: str) -> Optional[ContentInfo]:
        """提取页面内容
        
        Args:
            url: 页面URL
            html_content: HTML内容
            
        Returns:
            Optional[ContentInfo]: 内容信息
        """
        extractor = self.get_content_extractor()
        content = extractor.extract_content(url, html_content)
        
        if content and extractor.validate_content(content):
            return content
        
        return None
    
    def get_name(self) -> str:
        """获取框架名称
        
        Returns:
            str: 框架名称
        """
        return self.name
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"DocumentFramework(name='{self.name}')"


# 导出的公共接口
__all__ = [
    'PageInfo',
    'ContentInfo', 
    'FrameworkDetector',
    'MenuParser',
    'ContentExtractor',
    'DocumentFramework'
]