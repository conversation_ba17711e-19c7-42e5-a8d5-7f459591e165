#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片处理器

负责处理Markdown内容中的图片，包括链接保留、元数据提取和路径处理。

作者: Assistant
创建时间: 2025-07-30
"""

import re
from urllib.parse import urljoin, urlparse
from typing import List, Dict, Tuple, Optional
from pathlib import Path


class ImageProcessor:
    """图片处理器"""
    
    def __init__(self, base_url: str):
        """
        初始化图片处理器
        
        Args:
            base_url: 网站基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.parsed_base = urlparse(self.base_url)
        
        # 图片格式
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp', '.bmp', '.ico'}
        
        # 图片模式
        self.markdown_image_pattern = re.compile(r'!\[([^\]]*)\]\(([^\)]+)\)')
        self.html_image_pattern = re.compile(r'<img[^>]*src=["\']([^"\'>]+)["\'][^>]*alt=["\']([^"\'>]*)["\'][^>]*/?>', re.IGNORECASE)
    
    def process_images(self, markdown: str) -> Tuple[str, Dict[str, List]]:
        """
        处理Markdown中的所有图片
        
        Args:
            markdown: Markdown内容
            
        Returns:
            Tuple[str, Dict]: 处理后的Markdown内容和图片统计信息
        """
        image_stats = {
            'processed': [],
            'external': [],
            'internal': [],
            'invalid': []
        }
        
        # 处理Markdown格式的图片
        processed_markdown = self._process_markdown_images(markdown, image_stats)
        
        # 处理HTML格式的图片
        processed_markdown = self._process_html_images(processed_markdown, image_stats)
        
        return processed_markdown, image_stats
    
    def _process_markdown_images(self, markdown: str, image_stats: Dict) -> str:
        """处理Markdown格式的图片"""
        def replace_image(match):
            alt_text = match.group(1)
            src = match.group(2)
            
            # 处理图片源
            processed_src, image_info = self._process_single_image(src, alt_text)
            
            # 记录统计信息
            self._record_image_stats(src, processed_src, image_info, image_stats)
            
            return f'![{alt_text}]({processed_src})'
        
        return self.markdown_image_pattern.sub(replace_image, markdown)
    
    def _process_html_images(self, markdown: str, image_stats: Dict) -> str:
        """处理HTML格式的图片"""
        def replace_image(match):
            src = match.group(1)
            alt_text = match.group(2) if match.group(2) else ''
            
            # 处理图片源
            processed_src, image_info = self._process_single_image(src, alt_text)
            
            # 记录统计信息
            self._record_image_stats(src, processed_src, image_info, image_stats)
            
            return f'![{alt_text}]({processed_src})'
        
        return self.html_image_pattern.sub(replace_image, markdown)
    
    def _process_single_image(self, src: str, alt_text: str = '') -> Tuple[str, Dict]:
        """
        处理单个图片
        
        Args:
            src: 图片源URL
            alt_text: 替代文本
            
        Returns:
            Tuple[str, Dict]: (处理后的URL, 图片信息)
        """
        # 清理URL
        src = src.strip()
        
        # 图片信息
        image_info = {
            'original_src': src,
            'alt_text': alt_text,
            'type': 'unknown',
            'is_valid': False,
            'absolute_url': ''
        }
        
        try:
            # 解析URL
            parsed = urlparse(src)
            
            # 判断图片类型
            if self._is_valid_image_url(src):
                image_info['is_valid'] = True
                
                # 外部图片
                if parsed.netloc and parsed.netloc != self.parsed_base.netloc:
                    image_info['type'] = 'external'
                    image_info['absolute_url'] = src
                    return src, image_info
                
                # 内部图片
                elif self._is_internal_image(src):
                    image_info['type'] = 'internal'
                    # 转换为绝对URL
                    if not parsed.netloc:
                        absolute_url = urljoin(self.base_url, src)
                        image_info['absolute_url'] = absolute_url
                        return absolute_url, image_info
                    else:
                        image_info['absolute_url'] = src
                        return src, image_info
                
                # 相对路径图片
                else:
                    absolute_url = urljoin(self.base_url, src)
                    image_info['absolute_url'] = absolute_url
                    
                    # 判断是否为内部图片
                    if self._is_internal_image(absolute_url):
                        image_info['type'] = 'internal'
                    else:
                        image_info['type'] = 'external'
                    
                    return absolute_url, image_info
            
            # 无效图片URL
            else:
                image_info['type'] = 'invalid'
                return src, image_info
        
        except Exception as e:
            image_info['type'] = 'invalid'
            image_info['error'] = str(e)
            return src, image_info
    
    def _is_valid_image_url(self, url: str) -> bool:
        """
        判断是否为有效的图片URL
        
        Args:
            url: URL地址
            
        Returns:
            bool: 是否为有效图片URL
        """
        try:
            parsed = urlparse(url)
            path = parsed.path.lower()
            
            # 检查文件扩展名
            if any(path.endswith(ext) for ext in self.image_extensions):
                return True
            
            # 检查是否包含图片相关的路径
            image_keywords = ['image', 'img', 'picture', 'photo', 'asset']
            if any(keyword in path for keyword in image_keywords):
                return True
            
            # 检查查询参数中是否有图片格式
            if parsed.query:
                query_lower = parsed.query.lower()
                if any(ext[1:] in query_lower for ext in self.image_extensions):
                    return True
            
            return False
        
        except Exception:
            return False
    
    def _is_internal_image(self, url: str) -> bool:
        """
        判断是否为内部图片
        
        Args:
            url: URL地址
            
        Returns:
            bool: 是否为内部图片
        """
        try:
            parsed = urlparse(url)
            
            # 没有域名的相对链接
            if not parsed.netloc:
                return True
            
            # 同域名链接
            if parsed.netloc == self.parsed_base.netloc:
                return True
            
            return False
        
        except Exception:
            return False
    
    def _record_image_stats(self, original_src: str, processed_src: str, image_info: Dict, image_stats: Dict):
        """
        记录图片统计信息
        
        Args:
            original_src: 原始图片源
            processed_src: 处理后的图片源
            image_info: 图片信息
            image_stats: 统计信息字典
        """
        stats_entry = {
            'original_src': original_src,
            'processed_src': processed_src,
            'alt_text': image_info.get('alt_text', ''),
            'absolute_url': image_info.get('absolute_url', ''),
            'type': image_info.get('type', 'unknown')
        }
        
        # 添加到对应分类
        if image_info.get('is_valid', False):
            image_stats['processed'].append(stats_entry)
            
            if image_info.get('type') == 'external':
                image_stats['external'].append(stats_entry)
            elif image_info.get('type') == 'internal':
                image_stats['internal'].append(stats_entry)
        else:
            image_stats['invalid'].append(stats_entry)
    
    def extract_images_from_markdown(self, markdown: str) -> List[Dict]:
        """
        从Markdown中提取所有图片信息
        
        Args:
            markdown: Markdown内容
            
        Returns:
            List[Dict]: 图片信息列表
        """
        images = []
        
        # 提取Markdown格式的图片
        for match in self.markdown_image_pattern.finditer(markdown):
            images.append({
                'alt_text': match.group(1),
                'src': match.group(2),
                'format': 'markdown'
            })
        
        # 提取HTML格式的图片
        for match in self.html_image_pattern.finditer(markdown):
            images.append({
                'alt_text': match.group(2) if match.group(2) else '',
                'src': match.group(1),
                'format': 'html'
            })
        
        return images
    
    def get_image_metadata(self, src: str) -> Dict:
        """
        获取图片元数据
        
        Args:
            src: 图片源URL
            
        Returns:
            Dict: 图片元数据
        """
        metadata = {
            'src': src,
            'filename': '',
            'extension': '',
            'is_valid': False,
            'is_internal': False,
            'absolute_url': ''
        }
        
        try:
            parsed = urlparse(src)
            path = Path(parsed.path)
            
            metadata['filename'] = path.name
            metadata['extension'] = path.suffix.lower()
            metadata['is_valid'] = self._is_valid_image_url(src)
            metadata['is_internal'] = self._is_internal_image(src)
            
            # 生成绝对URL
            if not parsed.netloc:
                metadata['absolute_url'] = urljoin(self.base_url, src)
            else:
                metadata['absolute_url'] = src
        
        except Exception as e:
            metadata['error'] = str(e)
        
        return metadata
    
    def validate_images(self, images: List[str]) -> Dict[str, List[str]]:
        """
        验证图片URL有效性
        
        Args:
            images: 图片URL列表
            
        Returns:
            Dict[str, List[str]]: 分类后的图片URL
        """
        result = {
            'valid_internal': [],
            'valid_external': [],
            'invalid': []
        }
        
        for img_src in images:
            if self._is_valid_image_url(img_src):
                if self._is_internal_image(img_src):
                    result['valid_internal'].append(img_src)
                else:
                    result['valid_external'].append(img_src)
            else:
                result['invalid'].append(img_src)
        
        return result
    
    def clean_image_references(self, markdown: str) -> str:
        """
        清理Markdown中的无效图片引用
        
        Args:
            markdown: Markdown内容
            
        Returns:
            str: 清理后的Markdown内容
        """
        def clean_markdown_image(match):
            alt_text = match.group(1)
            src = match.group(2)
            
            if self._is_valid_image_url(src):
                return match.group(0)  # 保持原样
            else:
                # 移除无效图片，但保留alt文本作为普通文本
                return alt_text if alt_text else ''
        
        def clean_html_image(match):
            src = match.group(1)
            alt_text = match.group(2) if match.group(2) else ''
            
            if self._is_valid_image_url(src):
                return f'![{alt_text}]({src})'  # 转换为Markdown格式
            else:
                # 移除无效图片，但保留alt文本作为普通文本
                return alt_text if alt_text else ''
        
        # 清理Markdown格式的图片
        cleaned = self.markdown_image_pattern.sub(clean_markdown_image, markdown)
        
        # 清理HTML格式的图片
        cleaned = self.html_image_pattern.sub(clean_html_image, cleaned)
        
        return cleaned