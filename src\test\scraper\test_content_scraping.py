#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容抓取与处理模块测试

测试模块03的所有核心功能，包括内容抓取、转换、链接处理和图片处理。

作者: Assistant
创建时间: 2025-07-30
"""

import unittest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 导入被测试的模块
from src.scraper import (
    ScrapingConfig, ScrapingResult, ScrapingError,
    StarlightContentScraper, ScrapingManager, ScrapingTask,
    MarkdownConverter, LinkProcessor, ImageProcessor,
    create_scraper, scrape_content, create_manager
)


class TestScrapingConfig(unittest.TestCase):
    """测试抓取配置"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = ScrapingConfig()
        
        self.assertEqual(config.timeout, 30)
        self.assertEqual(config.max_retries, 3)
        self.assertEqual(config.retry_delay, 1.0)
        self.assertTrue(config.process_links)
        self.assertTrue(config.process_images)
        self.assertEqual(config.min_content_length, 100)
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = ScrapingConfig(
            timeout=60,
            max_retries=5,
            retry_delay=2.0,
            process_links=False,
            min_content_length=200
        )
        
        self.assertEqual(config.timeout, 60)
        self.assertEqual(config.max_retries, 5)
        self.assertEqual(config.retry_delay, 2.0)
        self.assertFalse(config.process_links)
        self.assertEqual(config.min_content_length, 200)
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试无效的超时时间
        with self.assertRaises(ValueError):
            ScrapingConfig(timeout=-1)
        
        # 测试无效的重试次数
        with self.assertRaises(ValueError):
            ScrapingConfig(max_retries=-1)
        
        # 测试无效的重试延迟
        with self.assertRaises(ValueError):
            ScrapingConfig(retry_delay=-1.0)


class TestScrapingResult(unittest.TestCase):
    """测试抓取结果"""
    
    def test_successful_result(self):
        """测试成功结果"""
        result = ScrapingResult(
            url='https://example.com',
            title='Test Page',
            content='# Test Content',
            metadata={'author': 'Test Author'},
            success=True,
            processing_time=1.5
        )
        
        self.assertEqual(result.url, 'https://example.com')
        self.assertEqual(result.title, 'Test Page')
        self.assertEqual(result.content, '# Test Content')
        self.assertTrue(result.success)
        self.assertTrue(result.is_valid())
        
        summary = result.get_summary()
        self.assertIn('成功', summary)
        self.assertIn('Test Page', summary)
    
    def test_failed_result(self):
        """测试失败结果"""
        result = ScrapingResult(
            url='https://example.com',
            title='',
            content='',
            metadata={},
            success=False,
            error='Connection timeout'
        )
        
        self.assertFalse(result.success)
        self.assertFalse(result.is_valid())
        self.assertEqual(result.error, 'Connection timeout')
        
        summary = result.get_summary()
        self.assertIn('失败', summary)
        self.assertIn('Connection timeout', summary)


class TestMarkdownConverter(unittest.TestCase):
    """测试Markdown转换器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = ScrapingConfig()
        self.converter = MarkdownConverter(self.config)
    
    def test_basic_conversion(self):
        """测试基本转换"""
        html = '<h1>Test Title</h1><p>Test paragraph with <strong>bold</strong> text.</p>'
        markdown = self.converter.convert(html)
        
        self.assertIn('# Test Title', markdown)
        self.assertIn('Test paragraph', markdown)
        self.assertIn('**bold**', markdown)
    
    def test_starlight_aside_conversion(self):
        """测试Starlight Aside组件转换"""
        html = '<div class="sl-aside caution"><p>This is a caution message.</p></div>'
        markdown = self.converter.convert(html)
        
        # 应该包含引用格式的警告信息
        self.assertIn('>', markdown)
        self.assertIn('caution', markdown.lower())
    
    def test_code_block_preservation(self):
        """测试代码块保留"""
        html = '<pre><code class="language-python">print("Hello, World!")</code></pre>'
        markdown = self.converter.convert(html)
        
        self.assertIn('```', markdown)
        self.assertIn('print("Hello, World!")', markdown)
    
    def test_table_conversion(self):
        """测试表格转换"""
        html = '''
        <table>
            <thead>
                <tr><th>Name</th><th>Age</th></tr>
            </thead>
            <tbody>
                <tr><td>Alice</td><td>25</td></tr>
                <tr><td>Bob</td><td>30</td></tr>
            </tbody>
        </table>
        '''
        markdown = self.converter.convert(html)
        
        self.assertIn('|', markdown)
        self.assertIn('Name', markdown)
        self.assertIn('Alice', markdown)


class TestLinkProcessor(unittest.TestCase):
    """测试链接处理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.base_url = 'https://docs.example.com'
        self.processor = LinkProcessor(self.base_url)
    
    def test_internal_link_conversion(self):
        """测试内部链接转换"""
        markdown = '[Getting Started](/getting-started/)'
        processed, stats = self.processor.process_links(markdown)
        
        self.assertIn('getting-started.md', processed)
        self.assertEqual(len(stats['internal']), 1)
        self.assertEqual(len(stats['converted']), 1)
    
    def test_external_link_preservation(self):
        """测试外部链接保留"""
        markdown = '[GitHub](https://github.com/example/repo)'
        processed, stats = self.processor.process_links(markdown)
        
        self.assertIn('https://github.com/example/repo', processed)
        self.assertEqual(len(stats['external']), 1)
    
    def test_anchor_link_preservation(self):
        """测试锚点链接保留"""
        markdown = '[Section](#section-title)'
        processed, stats = self.processor.process_links(markdown)
        
        self.assertIn('#section-title', processed)
        self.assertEqual(len(stats['anchors']), 1)
    
    def test_astro_to_md_conversion(self):
        """测试.astro到.md转换"""
        markdown = '[API Reference](/api/reference.astro)'
        processed, stats = self.processor.process_links(markdown)
        
        self.assertIn('reference.md', processed)
        self.assertNotIn('.astro', processed)
    
    def test_relative_path_calculation(self):
        """测试相对路径计算"""
        target_dir = Path('/docs/guides')
        markdown = '[API](/api/index.md)'
        processed, stats = self.processor.process_links(markdown, target_dir)
        
        # 应该计算出相对路径
        self.assertIn('../', processed)


class TestImageProcessor(unittest.TestCase):
    """测试图片处理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.base_url = 'https://docs.example.com'
        self.processor = ImageProcessor(self.base_url)
    
    def test_internal_image_processing(self):
        """测试内部图片处理"""
        markdown = '![Logo](/assets/logo.png)'
        processed, stats = self.processor.process_images(markdown)
        
        self.assertIn('https://docs.example.com/assets/logo.png', processed)
        self.assertEqual(len(stats['internal']), 1)
    
    def test_external_image_preservation(self):
        """测试外部图片保留"""
        markdown = '![External](https://cdn.example.com/image.jpg)'
        processed, stats = self.processor.process_images(markdown)
        
        self.assertIn('https://cdn.example.com/image.jpg', processed)
        self.assertEqual(len(stats['external']), 1)
    
    def test_html_image_conversion(self):
        """测试HTML图片转换"""
        markdown = '<img src="/assets/diagram.svg" alt="Diagram" />'
        processed, stats = self.processor.process_images(markdown)
        
        self.assertIn('![Diagram]', processed)
        self.assertIn('https://docs.example.com/assets/diagram.svg', processed)
    
    def test_invalid_image_handling(self):
        """测试无效图片处理"""
        markdown = '![Invalid](invalid-url)'
        processed, stats = self.processor.process_images(markdown)
        
        self.assertEqual(len(stats['invalid']), 1)


class TestStarlightContentScraper(unittest.TestCase):
    """测试Starlight内容抓取器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = ScrapingConfig(min_content_length=10)  # 降低最小内容长度以适应测试
        self.scraper = StarlightContentScraper(self.config)
    
    @patch('src.scraper.starlight_scraper.requests.Session.get')
    def test_successful_scraping(self, mock_get):
        """测试成功抓取"""
        # 模拟HTTP响应
        mock_response = Mock()
        mock_response.text = '''
        <html>
            <head><title>Test Page</title></head>
            <body>
                <main class="sl-markdown-content">
                    <h1>Test Title</h1>
                    <p>Test content with some text.</p>
                </main>
            </body>
        </html>
        '''
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = self.scraper.scrape('https://docs.example.com/test')
        
        self.assertTrue(result.success)
        self.assertEqual(result.title, 'Test Page')
        self.assertIn('Test Title', result.content)
        self.assertIn('Test content', result.content)
    
    @patch('src.scraper.starlight_scraper.requests.Session.get')
    def test_failed_scraping(self, mock_get):
        """测试抓取失败"""
        mock_get.side_effect = Exception('Connection error')
        
        result = self.scraper.scrape('https://docs.example.com/test')
        
        self.assertFalse(result.success)
        self.assertIn('Connection error', result.error)
    
    def test_content_element_extraction(self):
        """测试内容元素提取"""
        from bs4 import BeautifulSoup
        
        html = '''
        <html>
            <body>
                <nav>Navigation</nav>
                <main class="sl-markdown-content">
                    <h1>Main Content</h1>
                    <p>This is the main content.</p>
                </main>
                <footer>Footer</footer>
            </body>
        </html>
        '''
        
        soup = BeautifulSoup(html, 'html.parser')
        content_element = self.scraper._extract_content_element(soup)
        
        self.assertIsNotNone(content_element)
        self.assertIn('Main Content', content_element.get_text())
        self.assertNotIn('Navigation', content_element.get_text())
        self.assertNotIn('Footer', content_element.get_text())


class TestScrapingManager(unittest.TestCase):
    """测试抓取管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.config = ScrapingConfig()
        self.manager = ScrapingManager(self.config, max_workers=2)
    
    def test_task_addition(self):
        """测试任务添加"""
        task = ScrapingTask(url='https://example.com/page1')
        self.manager.add_task(task)
        
        progress = self.manager.get_progress()
        self.assertEqual(progress.total_tasks, 1)
    
    def test_batch_task_addition(self):
        """测试批量任务添加"""
        urls = [
            'https://example.com/page1',
            'https://example.com/page2',
            'https://example.com/page3'
        ]
        self.manager.add_urls(urls)
        
        progress = self.manager.get_progress()
        self.assertEqual(progress.total_tasks, 3)
    
    def test_progress_tracking(self):
        """测试进度跟踪"""
        progress = self.manager.get_progress()
        
        self.assertEqual(progress.total_tasks, 0)
        self.assertEqual(progress.completed_tasks, 0)
        self.assertEqual(progress.failed_tasks, 0)
        self.assertEqual(progress.progress_percentage, 0.0)
    
    def test_callback_setting(self):
        """测试回调函数设置"""
        progress_callback = Mock()
        result_callback = Mock()
        error_callback = Mock()
        
        self.manager.set_progress_callback(progress_callback)
        self.manager.set_result_callback(result_callback)
        self.manager.set_error_callback(error_callback)
        
        self.assertEqual(self.manager.progress_callback, progress_callback)
        self.assertEqual(self.manager.result_callback, result_callback)
        self.assertEqual(self.manager.error_callback, error_callback)


class TestConvenienceFunctions(unittest.TestCase):
    """测试便捷函数"""
    
    def test_create_scraper(self):
        """测试创建抓取器"""
        scraper = create_scraper()
        self.assertIsInstance(scraper, StarlightContentScraper)
        
        # 测试自定义配置
        config = ScrapingConfig(timeout=60)
        scraper = create_scraper(config)
        self.assertEqual(scraper.config.timeout, 60)
    
    def test_create_manager(self):
        """测试创建管理器"""
        manager = create_manager()
        self.assertIsInstance(manager, ScrapingManager)
        self.assertEqual(manager.max_workers, 4)
        
        # 测试自定义参数
        config = ScrapingConfig(timeout=60)
        manager = create_manager(config, max_workers=8)
        self.assertEqual(manager.config.timeout, 60)
        self.assertEqual(manager.max_workers, 8)
    
    @patch('src.scraper.StarlightContentScraper.scrape')
    def test_scrape_content(self, mock_scrape):
        """测试快速抓取内容"""
        # 模拟抓取结果
        mock_result = ScrapingResult(
            url='https://example.com',
            title='Test',
            content='# Test',
            metadata={},
            success=True
        )
        mock_scrape.return_value = mock_result
        
        result = scrape_content('https://example.com')
        
        self.assertEqual(result, mock_result)
        mock_scrape.assert_called_once_with('https://example.com')


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流程"""
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            target_dir = Path(temp_dir)
            
            # 创建配置
            config = ScrapingConfig(
                process_links=True,
                process_images=True,
                min_content_length=10
            )
            
            # 测试各组件协同工作
            converter = MarkdownConverter(config)
            link_processor = LinkProcessor('https://docs.example.com')
            image_processor = ImageProcessor('https://docs.example.com')
            
            # 测试HTML转换
            html = '''
            <h1>Test Document</h1>
            <p>This is a test with <a href="/guide/intro">internal link</a> and 
            <img src="/assets/logo.png" alt="Logo" />.</p>
            '''
            
            markdown = converter.convert(html)
            self.assertIn('# Test Document', markdown)
            
            # 测试链接处理
            processed_md, link_stats = link_processor.process_links(markdown)
            self.assertGreater(len(link_stats['internal']), 0)
            
            # 测试图片处理
            final_md, image_stats = image_processor.process_images(processed_md)
            self.assertGreater(len(image_stats['internal']), 0)
            
            # 验证最终结果
            self.assertIn('# Test Document', final_md)
            self.assertIn('guide/intro.md', final_md)
            self.assertIn('https://docs.example.com/assets/logo.png', final_md)


if __name__ == '__main__':
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestScrapingConfig,
        TestScrapingResult,
        TestMarkdownConverter,
        TestLinkProcessor,
        TestImageProcessor,
        TestStarlightContentScraper,
        TestScrapingManager,
        TestConvenienceFunctions,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    print(f"\n{'='*50}")
    print("测试结果摘要:")
    print(f"运行测试: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"成功率: {(result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100:.1f}%")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback.split('AssertionError: ')[-1].split('\n')[0]}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback.split('\n')[-2]}")