#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown转换器

负责将HTML内容转换为Markdown格式，针对Starlight框架进行优化。

作者: Assistant
创建时间: 2025-07-30
"""

import html2text
import re
from typing import Dict, List, Optional
from bs4 import BeautifulSoup, Tag
from .base import ScrapingConfig


class MarkdownConverter:
    """Markdown转换器"""
    
    def __init__(self, config: Optional[ScrapingConfig] = None):
        self.config = config or ScrapingConfig()
        self._setup_html2text()
    
    def _setup_html2text(self):
        """配置html2text转换器"""
        self.h2t = html2text.HTML2Text()
        
        # 基础配置
        self.h2t.ignore_links = False
        self.h2t.ignore_images = False
        self.h2t.ignore_emphasis = False
        self.h2t.body_width = 0  # 不限制行宽
        self.h2t.unicode_snob = True  # 使用Unicode字符
        self.h2t.escape_all = False  # 不转义所有字符
        
        # 代码块配置
        self.h2t.protect_links = True
        self.h2t.wrap_links = False
        
        # 表格配置
        self.h2t.ignore_tables = False
        
    def convert(self, html: str, config: Optional[ScrapingConfig] = None) -> str:
        """转换HTML为Markdown
        
        Args:
            html: HTML内容
            config: 转换配置，如果为None则使用初始化时的配置
            
        Returns:
            str: Markdown内容
        """
        # 使用传入的配置或默认配置
        active_config = config or self.config
        if not html or not html.strip():
            return ""
        
        # 应用配置
        self._apply_config(active_config)
        
        # 预处理HTML
        processed_html = self._preprocess_html(html)
        
        # 转换为Markdown
        markdown = self.h2t.handle(processed_html)
        
        # 后处理Markdown
        markdown = self._postprocess_markdown(markdown)
        
        return markdown.strip()
    
    def _apply_config(self, config: Optional[ScrapingConfig]):
        """应用转换配置"""
        if config is None:
            return
        self.h2t.body_width = config.body_width
        self.h2t.unicode_snob = config.unicode_snob
        self.h2t.escape_all = config.escape_all
    
    def _preprocess_html(self, html: str) -> str:
        """预处理HTML内容"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # 处理Starlight特有组件
        self._process_starlight_components(soup)
        
        # 处理代码块
        self._process_code_blocks(soup)
        
        # 处理表格
        self._process_tables(soup)
        
        # 处理链接
        self._process_links(soup)
        
        return str(soup)
    
    def _process_starlight_components(self, soup: BeautifulSoup):
        """处理Starlight特有组件"""
        # 处理徽章组件 .sl-badge
        for badge in soup.find_all(class_='sl-badge'):
            badge_text = badge.get_text().strip()
            if badge_text:
                # 转换为Markdown徽章格式
                new_span = soup.new_tag('span')
                new_span.string = f'`{badge_text}`'
                badge.replace_with(new_span)
        
        # 处理提示框组件 .aside 和 .sl-aside
        for aside in soup.find_all(class_=['aside', 'sl-aside']):
            aside_type = self._get_aside_type(aside)
            aside_content = aside.get_text().strip()
            if aside_content:
                # 转换为Markdown提示框格式
                new_div = soup.new_tag('div')
                new_div.string = f'> **{aside_type}**: {aside_content}'
                aside.replace_with(new_div)
        
        # 处理步骤组件 .steps
        for steps in soup.find_all(class_='steps'):
            # 保持原有的有序列表结构
            if steps.name != 'ol':
                steps.name = 'ol'
    
    def _get_aside_type(self, aside: Tag) -> str:
        """获取提示框类型"""
        classes = aside.get('class', [])
        for cls in classes:
            if cls.startswith('aside-'):
                return cls.replace('aside-', '').title()
        return 'Note'
    
    def _process_code_blocks(self, soup: BeautifulSoup):
        """处理代码块"""
        # 处理Starlight的代码块 .expressive-code
        for code_block in soup.find_all(class_='expressive-code'):
            # 查找实际的代码内容
            code_element = code_block.find('code')
            if code_element:
                # 保留语言信息
                lang_class = self._extract_language_class(code_element)
                if lang_class:
                    code_element['data-lang'] = lang_class
        
        # 处理普通代码块 - 手动转换为Markdown格式
        for pre in soup.find_all('pre'):
            code = pre.find('code')
            if code:
                # 提取语言和代码内容
                lang = self._extract_language_class(code) or ''
                code_content = code.get_text()
                
                # 创建Markdown代码块
                markdown_code = f'```{lang}\n{code_content}\n```'
                
                # 替换原始的pre标签
                new_div = soup.new_tag('div')
                new_div.string = markdown_code
                pre.replace_with(new_div)
    
    def _extract_language_class(self, code_element: Tag) -> Optional[str]:
        """提取代码语言类名"""
        classes = code_element.get('class', [])
        for cls in classes:
            if cls.startswith('language-'):
                return cls.replace('language-', '')
            elif cls.startswith('lang-'):
                return cls.replace('lang-', '')
        return None
    
    def _process_tables(self, soup: BeautifulSoup):
        """处理表格"""
        for table in soup.find_all('table'):
            # 确保表格有正确的结构
            if not table.find('thead') and table.find('tr'):
                # 如果没有thead，将第一行转换为thead
                first_row = table.find('tr')
                if first_row:
                    thead = soup.new_tag('thead')
                    tbody = soup.new_tag('tbody')
                    
                    # 移动第一行到thead
                    first_row.extract()
                    thead.append(first_row)
                    
                    # 移动其余行到tbody
                    for row in table.find_all('tr'):
                        row.extract()
                        tbody.append(row)
                    
                    # 重新组织表格
                    table.clear()
                    table.append(thead)
                    table.append(tbody)
    
    def _process_links(self, soup: BeautifulSoup):
        """处理链接"""
        for link in soup.find_all('a', href=True):
            href = link['href']
            # 保留原始链接，后续由LinkProcessor处理
            link['data-original-href'] = href
    
    def _postprocess_markdown(self, markdown: str) -> str:
        """后处理Markdown内容"""
        # 修复html2text生成的错误链接格式
        markdown = self._fix_html2text_links(markdown)
        
        # 清理多余的空行
        markdown = re.sub(r'\n{3,}', '\n\n', markdown)
        
        # 修复代码块格式
        markdown = self._fix_code_blocks(markdown)
        
        # 修复表格格式
        markdown = self._fix_tables(markdown)
        
        # 修复列表格式
        markdown = self._fix_lists(markdown)
        
        return markdown
    
    def _fix_html2text_links(self, markdown: str) -> str:
        """修复html2text生成的错误链接格式"""
        # 修复形如 [text](<url>) 的链接格式，移除URL周围的尖括号
        markdown = re.sub(r'\[([^\]]+)\]\(<([^>]+)>\)', r'[\1](\2)', markdown)
        return markdown
    
    def _fix_code_blocks(self, markdown: str) -> str:
        """修复代码块格式"""
        # 确保代码块前后有空行
        markdown = re.sub(r'([^\n])\n```', r'\1\n\n```', markdown)
        markdown = re.sub(r'```\n([^\n])', r'```\n\n\1', markdown)
        
        return markdown
    
    def _fix_tables(self, markdown: str) -> str:
        """修复表格格式"""
        # 确保表格前后有空行
        lines = markdown.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            if '|' in line and line.strip().startswith('|'):
                # 这是表格行
                if i > 0 and lines[i-1].strip() and '|' not in lines[i-1]:
                    fixed_lines.append('')  # 在表格前添加空行
                fixed_lines.append(line)
                if i < len(lines) - 1 and lines[i+1].strip() and '|' not in lines[i+1]:
                    fixed_lines.append('')  # 在表格后添加空行
            else:
                fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)
    
    def _fix_lists(self, markdown: str) -> str:
        """修复列表格式"""
        # 确保列表项之间的间距正确
        lines = markdown.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            fixed_lines.append(line)
            
            # 检查是否是列表项
            if re.match(r'^\s*[\*\-\+]\s', line) or re.match(r'^\s*\d+\.\s', line):
                # 如果下一行不是列表项且不为空，添加空行
                if (i < len(lines) - 1 and 
                    lines[i+1].strip() and 
                    not re.match(r'^\s*[\*\-\+]\s', lines[i+1]) and 
                    not re.match(r'^\s*\d+\.\s', lines[i+1])):
                    fixed_lines.append('')
        
        return '\n'.join(fixed_lines)