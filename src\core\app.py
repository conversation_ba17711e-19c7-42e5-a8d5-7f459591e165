#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主应用程序类 - 实现基础架构

本模块实现了技术文档抓取工具的主应用程序类，包括：
- tkinter GUI界面
- 多线程架构
- 核心操作流程（预览、开始、停止）
- 扩展性设计
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
from pathlib import Path
from typing import Optional, Dict, Any
import logging
import os
import re
import urllib.parse
import requests
import webbrowser
from urllib.parse import urlparse

# 导入utils模块
from utils.logger import setup_logger, add_gui_callback, LogLevel
from utils.task_manager import TaskManager, TaskStatus
from utils.resource_manager import get_resource_manager, ResourceType
# 延迟导入，避免相对导入问题
# from ..scraper import ScrapingManager, ScrapingConfig

class DocumentScraperApp:
    """技术文档抓取工具主应用程序类"""
    
    def __init__(self):
        """初始化应用程序"""
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("技术文档网站转Markdown工具 v1.0")
        self.root.geometry("900x700")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
            
        # 应用程序状态
        self.is_running = False
        self.current_task = None
        
        # 任务管理相关
        self.task_manager = TaskManager(max_workers=3, logger=None)  # logger将在_init_logging中设置
        self.result_queue = queue.Queue()
        self.scraping_task_id: Optional[str] = None
        
        # 资源管理器
        self.resource_manager = get_resource_manager()
        
        # 配置变量
        self.url_var = tk.StringVar()
        self.output_dir_var = tk.StringVar(value=str(Path.home() / "Documents" / "scraped_docs"))
        self.thread_count_var = tk.IntVar(value=3)
        self.delay_var = tk.DoubleVar(value=1.0)
        
        # 验证状态变量
        self.url_valid = tk.BooleanVar(value=False)
        self.output_dir_valid = tk.BooleanVar(value=True)
        
        # 绑定验证事件
        self.url_var.trace('w', self._validate_url)
        self.output_dir_var.trace('w', self._validate_output_dir)
        
        # 初始化GUI
        self._init_gui()
        
        # 初始化日志
        self._init_logging()
        
        # 启动结果处理定时器
        self._start_result_processor()
        
    def _init_gui(self):
        """初始化GUI界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 创建配置面板
        self._create_config_panel(main_frame)
        
        # 创建预览区域
        self._create_preview_panel(main_frame)
        
        # 创建操作控制面板
        self._create_control_panel(main_frame)
        
        # 创建进度监控面板
        self._create_progress_panel(main_frame)
        
        # 创建日志显示面板
        self._create_log_panel(main_frame)
        
    def _create_config_panel(self, parent):
        """创建配置面板"""
        config_frame = ttk.LabelFrame(parent, text="配置设置", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # URL输入
        ttk.Label(config_frame, text="目标URL:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        url_frame = ttk.Frame(config_frame)
        url_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        url_frame.columnconfigure(0, weight=1)
        
        url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=50)
        url_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # URL验证状态指示器
        self.url_status_label = ttk.Label(url_frame, text="⚪", foreground="gray")
        self.url_status_label.grid(row=0, column=1)
        
        # 输出目录选择
        ttk.Label(config_frame, text="输出目录:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        output_frame = ttk.Frame(config_frame)
        output_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(10, 0))
        output_frame.columnconfigure(0, weight=1)
        
        ttk.Entry(output_frame, textvariable=self.output_dir_var).grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        ttk.Button(output_frame, text="浏览", command=self._browse_output_dir).grid(row=0, column=1, padx=(0, 10))
        
        # 输出目录验证状态指示器
        self.output_status_label = ttk.Label(output_frame, text="✓", foreground="green")
        self.output_status_label.grid(row=0, column=2)
        
        # 基础配置行
        basic_frame = ttk.Frame(config_frame)
        basic_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 线程数量设置
        ttk.Label(basic_frame, text="线程数量:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        thread_frame = ttk.Frame(basic_frame)
        thread_frame.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        ttk.Scale(thread_frame, from_=1, to=5, variable=self.thread_count_var, orient=tk.HORIZONTAL, length=150).grid(row=0, column=0, padx=(0, 10))
        ttk.Label(thread_frame, textvariable=self.thread_count_var).grid(row=0, column=1)
        
        # 抓取间隔设置
        ttk.Label(basic_frame, text="抓取间隔(秒):").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        delay_frame = ttk.Frame(basic_frame)
        delay_frame.grid(row=0, column=3, sticky=tk.W)
        
        ttk.Scale(delay_frame, from_=0.5, to=3.0, variable=self.delay_var, orient=tk.HORIZONTAL, length=150).grid(row=0, column=0, padx=(0, 10))
        ttk.Label(delay_frame, textvariable=self.delay_var).grid(row=0, column=1)
        
        # 高级设置按钮
        self.show_advanced = tk.BooleanVar(value=False)
        advanced_btn = ttk.Checkbutton(config_frame, text="显示高级设置", variable=self.show_advanced, command=self._toggle_advanced_settings)
        advanced_btn.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(10, 0))
        
        # 高级设置面板（默认隐藏）
        self.advanced_frame = ttk.LabelFrame(config_frame, text="高级设置", padding="10")
        self.advanced_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        self.advanced_frame.grid_remove()  # 初始隐藏
        self.advanced_frame.columnconfigure(1, weight=1)
        self.advanced_frame.columnconfigure(3, weight=1)
        
        # 请求头配置
        ttk.Label(self.advanced_frame, text="User-Agent:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.user_agent = tk.StringVar(value="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        user_agent_entry = ttk.Entry(self.advanced_frame, textvariable=self.user_agent, width=50)
        user_agent_entry.grid(row=0, column=1, columnspan=3, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 代理设置
        ttk.Label(self.advanced_frame, text="代理服务器:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.proxy_url = tk.StringVar()
        proxy_entry = ttk.Entry(self.advanced_frame, textvariable=self.proxy_url, width=30)
        proxy_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 20), pady=(10, 0))
        
        # 超时设置
        ttk.Label(self.advanced_frame, text="请求超时(秒):").grid(row=1, column=2, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.request_timeout = tk.IntVar(value=30)
        timeout_spinbox = ttk.Spinbox(self.advanced_frame, from_=5, to=120, textvariable=self.request_timeout, width=10)
        timeout_spinbox.grid(row=1, column=3, sticky=tk.W, pady=(10, 0))
        
        # 重试设置
        ttk.Label(self.advanced_frame, text="重试次数:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.retry_count = tk.IntVar(value=3)
        retry_spinbox = ttk.Spinbox(self.advanced_frame, from_=0, to=10, textvariable=self.retry_count, width=10)
        retry_spinbox.grid(row=2, column=1, sticky=tk.W, padx=(0, 20), pady=(10, 0))
        
        # 深度限制
        ttk.Label(self.advanced_frame, text="抓取深度:").grid(row=2, column=2, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.max_depth = tk.IntVar(value=5)
        depth_spinbox = ttk.Spinbox(self.advanced_frame, from_=1, to=20, textvariable=self.max_depth, width=10)
        depth_spinbox.grid(row=2, column=3, sticky=tk.W, pady=(10, 0))
        
        # 文件类型过滤
        filter_frame = ttk.Frame(self.advanced_frame)
        filter_frame.grid(row=3, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(10, 0))
        filter_frame.columnconfigure(1, weight=1)
        filter_frame.columnconfigure(3, weight=1)
        
        ttk.Label(filter_frame, text="包含文件类型:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.include_extensions = tk.StringVar(value=".html,.htm,.php,.asp,.jsp")
        ext_entry = ttk.Entry(filter_frame, textvariable=self.include_extensions, width=30)
        ext_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 20))
        
        ttk.Label(filter_frame, text="排除关键词:").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.exclude_keywords = tk.StringVar(value="login,admin,download")
        exclude_entry = ttk.Entry(filter_frame, textvariable=self.exclude_keywords, width=30)
        exclude_entry.grid(row=0, column=3, sticky=(tk.W, tk.E))
        
    def _create_preview_panel(self, parent):
        """创建预览区域"""
        preview_frame = ttk.LabelFrame(parent, text="菜单预览", padding="10")
        preview_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5), pady=(0, 10))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(1, weight=1)
        
        # 预览统计信息
        stats_frame = ttk.Frame(preview_frame)
        stats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.total_pages_var = tk.StringVar(value="总页面: 0")
        self.estimated_time_var = tk.StringVar(value="预估时间: --")
        
        ttk.Label(stats_frame, textvariable=self.total_pages_var).grid(row=0, column=0, padx=(0, 20))
        ttk.Label(stats_frame, textvariable=self.estimated_time_var).grid(row=0, column=1)
        
        # 创建树形视图
        tree_frame = ttk.Frame(preview_frame)
        tree_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        self.preview_tree = ttk.Treeview(tree_frame, columns=("status", "url"), show="tree headings")
        self.preview_tree.heading("#0", text="页面结构")
        self.preview_tree.heading("status", text="状态")
        self.preview_tree.heading("url", text="URL")
        
        self.preview_tree.column("#0", width=200)
        self.preview_tree.column("status", width=80)
        self.preview_tree.column("url", width=300)
        
        # 添加滚动条
        preview_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.preview_tree.yview)
        self.preview_tree.configure(yscrollcommand=preview_scrollbar.set)
        
        self.preview_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        preview_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 绑定双击事件
        self.preview_tree.bind("<Double-1>", self._on_tree_double_click)
        
    def _create_control_panel(self, parent):
        """创建操作控制面板"""
        control_frame = ttk.LabelFrame(parent, text="操作控制", padding="10")
        control_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N), padx=(5, 0), pady=(0, 10))
        
        # 预览按钮
        self.preview_btn = ttk.Button(control_frame, text="预览菜单", command=self._preview_menu)
        self.preview_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 开始按钮
        self.start_btn = ttk.Button(control_frame, text="开始抓取", command=self._start_scraping)
        self.start_btn.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 停止按钮
        self.stop_btn = ttk.Button(control_frame, text="停止抓取", command=self._stop_scraping, state=tk.DISABLED)
        self.stop_btn.grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        # 配置按钮宽度
        for btn in [self.preview_btn, self.start_btn, self.stop_btn]:
            btn.configure(width=15)
            
    def _create_progress_panel(self, parent):
        """创建进度监控面板"""
        progress_frame = ttk.LabelFrame(parent, text="进度监控", padding="10")
        progress_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        # 当前状态标签
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(progress_frame, textvariable=self.status_var).grid(row=0, column=0, sticky=tk.W)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        # 统计信息
        stats_frame = ttk.Frame(progress_frame)
        stats_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.success_var = tk.StringVar(value="成功: 0")
        self.failed_var = tk.StringVar(value="失败: 0")
        self.skipped_var = tk.StringVar(value="跳过: 0")
        
        ttk.Label(stats_frame, textvariable=self.success_var).grid(row=0, column=0, padx=(0, 20))
        ttk.Label(stats_frame, textvariable=self.failed_var).grid(row=0, column=1, padx=(0, 20))
        ttk.Label(stats_frame, textvariable=self.skipped_var).grid(row=0, column=2)
        
    def _create_log_panel(self, parent):
        """创建日志显示面板"""
        log_frame = ttk.LabelFrame(parent, text="操作日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(1, weight=1)
        
        # 日志控制面板
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        log_control_frame.columnconfigure(1, weight=1)
        
        # 日志级别过滤
        ttk.Label(log_control_frame, text="级别:").grid(row=0, column=0, padx=(0, 5))
        self.log_level_var = tk.StringVar(value="全部")
        log_level_combo = ttk.Combobox(log_control_frame, textvariable=self.log_level_var, 
                                     values=["全部", "INFO", "WARNING", "ERROR"], 
                                     state="readonly", width=10)
        log_level_combo.grid(row=0, column=1, padx=(0, 10), sticky=tk.W)
        log_level_combo.bind("<<ComboboxSelected>>", self._filter_logs)
        
        # 搜索框
        ttk.Label(log_control_frame, text="搜索:").grid(row=0, column=2, padx=(0, 5))
        self.log_search_var = tk.StringVar()
        search_entry = ttk.Entry(log_control_frame, textvariable=self.log_search_var, width=20)
        search_entry.grid(row=0, column=3, padx=(0, 10))
        search_entry.bind("<KeyRelease>", self._filter_logs)
        
        # 清空按钮
        ttk.Button(log_control_frame, text="清空", command=self._clear_logs).grid(row=0, column=4)
        
        # 创建文本框
        text_frame = ttk.Frame(log_frame)
        text_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(text_frame, height=10, wrap=tk.WORD, font=('Consolas', 9))
        log_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置日志颜色标签
        self.log_text.tag_configure("INFO", foreground="black")
        self.log_text.tag_configure("WARNING", foreground="orange")
        self.log_text.tag_configure("ERROR", foreground="red")
        self.log_text.tag_configure("SUCCESS", foreground="green")
        
        # 存储所有日志条目用于过滤
        self.all_log_entries = []
        
        # 配置主框架的行权重
        parent.rowconfigure(3, weight=1)
        
    def _init_logging(self):
        """初始化日志系统"""
        # 定义GUI回调函数
        def gui_log_callback(msg, level):
            # 在主线程中更新GUI
            self.root.after(0, lambda: self._append_log(msg, level))
        
        # 使用utils.logger模块设置日志系统
        self.logger = setup_logger(
            name="DocumentScraper",
            level=LogLevel.INFO,
            console_output=True,
            gui_callback=gui_log_callback,
            format_string="%(asctime)s - %(levelname)s - %(message)s",
            date_format="%H:%M:%S"
        )
        
        # 设置任务管理器的logger并启动
        self.task_manager.logger = self.logger
        self.task_manager.start()
        
        # 启动资源管理器的内存监控
        self.resource_manager.start_memory_monitoring(
            interval=10.0,  # 每10秒检查一次
            warning_threshold=80.0,
            critical_threshold=90.0
        )
        
    def _start_result_processor(self):
        """启动结果处理定时器"""
        self._process_results()
        
    def _process_results(self):
        """处理来自工作线程的结果"""
        try:
            while True:
                result = self.result_queue.get_nowait()
                self._handle_result(result)
        except queue.Empty:
            pass
        finally:
            # 每100ms检查一次结果队列
            self.root.after(100, self._process_results)
            
    def _handle_result(self, result: Dict[str, Any]):
        """处理单个结果"""
        result_type = result.get("type")
        
        if result_type == "progress":
            self._update_progress(result)
        elif result_type == "status":
            self._update_status(result)
        elif result_type == "log":
            self.logger.info(result.get("message", ""))
        elif result_type == "error":
            self.logger.error(result.get("message", ""))
        elif result_type == "complete":
            self._task_completed(result)
            
    def _update_progress(self, result: Dict[str, Any]):
        """更新进度显示"""
        progress = result.get("progress", 0)
        self.progress_var.set(progress)
        
        # 更新统计信息
        stats = result.get("stats", {})
        self.success_var.set(f"成功: {stats.get('success', 0)}")
        self.failed_var.set(f"失败: {stats.get('failed', 0)}")
        self.skipped_var.set(f"跳过: {stats.get('skipped', 0)}")
        
    def _update_status(self, result: Dict[str, Any]):
        """更新状态显示"""
        status = result.get("status", "")
        self.status_var.set(status)
        
    def _task_completed(self, result: Dict[str, Any]):
        """任务完成处理"""
        self.is_running = False
        self._update_button_states()
        self.status_var.set("任务完成")
        self.logger.info("抓取任务已完成")
        
    def _browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(initialdir=self.output_dir_var.get())
        if directory:
            self.output_dir_var.set(directory)
            
    def _preview_menu(self):
        """预览菜单结构"""
        url = self.url_var.get().strip()
        
        # 验证已经在实时进行，这里直接检查验证状态
        if not self.url_valid.get():
            messagebox.showwarning("警告", "请输入有效的目标URL")
            return
            
        self.logger.info(f"开始预览菜单结构: {url}")
        self.status_var.set("正在解析网站结构...")
        
        # 禁用预览按钮防止重复点击
        self.preview_btn.config(state='disabled')
        
        # 在后台线程中执行预览
        def preview_worker():
            try:
                # 导入框架管理器
                from ..frameworks.manager import get_framework_manager
                import requests
                
                # 获取网站内容
                response = requests.get(url, timeout=10)
                response.raise_for_status()
                html_content = response.text
                
                # 检测框架
                manager = get_framework_manager()
                framework_name = manager.detect_framework(url, html_content)
                
                if not framework_name:
                    self.root.after(0, lambda: self._show_preview_error("未检测到支持的文档框架"))
                    return
                    
                # 解析菜单结构
                pages = manager.parse_menu_structure(framework_name, url, html_content)
                
                if not pages:
                    self.root.after(0, lambda: self._show_preview_error("未找到菜单结构"))
                    return
                    
                # 在主线程中更新UI
                self.root.after(0, lambda: self._populate_preview_tree_with_data(pages, framework_name))
                
            except Exception as e:
                error_msg = f"预览失败: {str(e)}"
                self.root.after(0, lambda: self._show_preview_error(error_msg))
        
        # 启动后台线程
        thread = threading.Thread(target=preview_worker, daemon=True)
        thread.start()
        
    def _show_preview_error(self, error_msg):
        """显示预览错误"""
        self.status_var.set("预览失败")
        self.preview_btn.config(state='normal')
        messagebox.showerror("预览失败", error_msg)
        self.logger.error(error_msg)
        
    def _populate_preview_tree_with_data(self, pages, framework_name):
        """使用真实数据填充预览树"""
        # 清空现有内容
        for item in self.preview_tree.get_children():
            self.preview_tree.delete(item)
            
        # 构建树形结构
        tree_data = self._build_tree_structure(pages)
        
        # 添加到树形控件
        self._add_tree_items("", tree_data)
        
        # 更新统计信息
        total_pages = len(pages)
        self.total_pages_var.set(f"总页面: {total_pages}")
        
        # 计算预估时间（基于线程数和延迟）
        delay = self.delay_var.get()
        threads = self.thread_count_var.get()
        estimated_seconds = (total_pages * delay) / threads
        estimated_minutes = int(estimated_seconds / 60)
        estimated_seconds = int(estimated_seconds % 60)
        
        if estimated_minutes > 0:
            self.estimated_time_var.set(f"预估时间: {estimated_minutes}分{estimated_seconds}秒")
        else:
            self.estimated_time_var.set(f"预估时间: {estimated_seconds}秒")
        
        # 恢复按钮状态
        self.preview_btn.config(state='normal')
        self.status_var.set("菜单结构预览完成")
        self.logger.info(f"成功解析 {total_pages} 个页面 (框架: {framework_name})")
        
    def _build_tree_structure(self, pages):
        """构建树形结构数据"""
        # 将扁平的页面列表转换为树形结构
        tree_data = []
        
        for page in pages:
            tree_item = {
                "title": page.title,
                "url": page.url,
                "children": [],
                "level": getattr(page, 'level', 0)
            }
            tree_data.append(tree_item)
            
        return tree_data
        
    def _add_tree_items(self, parent, items):
        """递归添加树形项目"""
        for item in items:
            tree_item = self.preview_tree.insert(parent, "end", 
                                                text=item["title"], 
                                                values=("待抓取", item["url"]))
            if item.get("children"):
                self._add_tree_items(tree_item, item["children"])
                
    def _populate_preview_tree(self):
        """填充预览树（临时示例）"""
        # 清空现有内容
        for item in self.preview_tree.get_children():
            self.preview_tree.delete(item)
            
        # 添加示例数据
        base_url = self.url_var.get().strip()
        root_item = self.preview_tree.insert("", "end", text="文档根目录", 
                                           values=("待抓取", base_url))
        
        # 快速开始部分
        guide_item = self.preview_tree.insert(root_item, "end", text="快速开始", 
                                            values=("待抓取", f"{base_url}/guide"))
        self.preview_tree.insert(guide_item, "end", text="安装指南", 
                               values=("待抓取", f"{base_url}/guide/installation"))
        self.preview_tree.insert(guide_item, "end", text="基础配置", 
                               values=("待抓取", f"{base_url}/guide/configuration"))
        self.preview_tree.insert(guide_item, "end", text="快速上手", 
                               values=("待抓取", f"{base_url}/guide/getting-started"))
        
        # API文档部分
        api_item = self.preview_tree.insert(root_item, "end", text="API文档", 
                                          values=("待抓取", f"{base_url}/api"))
        self.preview_tree.insert(api_item, "end", text="认证", 
                               values=("待抓取", f"{base_url}/api/auth"))
        self.preview_tree.insert(api_item, "end", text="用户管理", 
                               values=("待抓取", f"{base_url}/api/users"))
        self.preview_tree.insert(api_item, "end", text="数据接口", 
                               values=("待抓取", f"{base_url}/api/data"))
        
        # 高级功能部分
        advanced_item = self.preview_tree.insert(root_item, "end", text="高级功能", 
                                                values=("待抓取", f"{base_url}/advanced"))
        self.preview_tree.insert(advanced_item, "end", text="插件开发", 
                               values=("待抓取", f"{base_url}/advanced/plugins"))
        self.preview_tree.insert(advanced_item, "end", text="自定义主题", 
                               values=("待抓取", f"{base_url}/advanced/themes"))
        
        # 展开根节点
        self.preview_tree.item(root_item, open=True)
        
        # 更新统计信息
        total_pages = self._count_tree_items()
        self.total_pages_var.set(f"总页面: {total_pages}")
        
        # 计算预估时间（基于线程数和延迟）
        delay = self.delay_var.get()
        threads = self.thread_count_var.get()
        estimated_seconds = (total_pages * delay) / threads
        estimated_minutes = int(estimated_seconds / 60)
        estimated_seconds = int(estimated_seconds % 60)
        
        if estimated_minutes > 0:
            self.estimated_time_var.set(f"预估时间: {estimated_minutes}分{estimated_seconds}秒")
        else:
            self.estimated_time_var.set(f"预估时间: {estimated_seconds}秒")
        
    def _start_scraping(self):
        """开始抓取"""
        url = self.url_var.get().strip()
        output_dir = self.output_dir_var.get().strip()
        
        # 验证已经在实时进行，这里直接检查验证状态
        if not self.url_valid.get() or not self.output_dir_valid.get():
            messagebox.showwarning("警告", "请确保URL和输出目录都有效")
            return
            
        # 检查是否已预览
        if not self.preview_tree.get_children():
            messagebox.showwarning("警告", "请先预览菜单结构")
            return
            
        # 创建输出目录
        try:
            Path(output_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            messagebox.showerror("错误", f"无法创建输出目录: {e}")
            return
            
        self.is_running = True
        self._update_button_states()
        self.status_var.set("正在启动抓取任务...")
        self.logger.info(f"开始抓取任务: {url} -> {output_dir}")
        self.logger.info(f"配置参数: 线程数={self.thread_count_var.get()}, 间隔={self.delay_var.get()}秒")
        
        # 使用TaskManager启动抓取任务
        def scraping_worker():
            try:
                # 导入必要模块
                import sys
                from pathlib import Path
                
                # 添加src路径以支持绝对导入
                src_path = Path(__file__).parent.parent
                if str(src_path) not in sys.path:
                    sys.path.insert(0, str(src_path))
                    
                from scraper.manager import ScrapingManager, ScrapingTask
                from scraper.base import ScrapingConfig
                
                # 创建抓取配置
                config = ScrapingConfig(
                    base_url=self.url_var.get().strip(),
                    delay=self.delay_var.get(),
                    timeout=self.request_timeout.get(),
                    max_retries=self.retry_count.get()
                )
                
                # 创建抓取管理器
                manager = ScrapingManager(config, max_workers=self.thread_count_var.get())
                
                # 设置回调函数
                manager.set_progress_callback(self._on_progress_update)
                manager.set_result_callback(self._on_scraping_result)
                manager.set_error_callback(self._on_scraping_error)
                
                # 收集抓取任务
                tasks = self._collect_scraping_tasks()
                manager.add_tasks(tasks)
                
                # 在主线程中更新状态
                self.root.after(0, lambda: self._set_status("正在抓取..."))
                
                # 开始抓取
                for result in manager.start_scraping():
                    if not self.is_running:  # 检查是否被停止
                        manager.stop()
                        break
                        
                # 抓取完成
                if self.is_running:  # 正常完成
                    self.root.after(0, self._on_scraping_completed)
                    
            except Exception as e:
                error_msg = f"抓取失败: {str(e)}"
                self.root.after(0, lambda: self._on_scraping_error("系统错误", e))
        
        # 使用TaskManager添加抓取任务
        self.scraping_task_id = self.task_manager.add_task(
            name="网站抓取任务",
            func=scraping_worker
        )
        
    def _stop_scraping(self):
        """停止抓取"""
        if not self.is_running:
            return
            
        self.is_running = False
        self._update_button_states()
        
        # 使用TaskManager取消抓取任务
        if self.scraping_task_id:
            self.task_manager.cancel_task(self.scraping_task_id)
            self.scraping_task_id = None
            
        self.status_var.set("抓取任务已停止")
        self.logger.info("停止抓取任务")
        
        # 重置进度
        self.progress_var.set(0)
        self.success_var.set("成功: 0")
        self.failed_var.set("失败: 0")
        self.skipped_var.set("跳过: 0")
            
    def _update_button_states(self):
        """更新按钮状态"""
        if self.is_running:
            self.preview_btn.configure(state=tk.DISABLED)
            self.start_btn.configure(state=tk.DISABLED)
            self.stop_btn.configure(state=tk.NORMAL)
        else:
            # 只有URL有效时才能预览
            if self.url_valid.get():
                self.preview_btn.configure(state=tk.NORMAL)
            else:
                self.preview_btn.configure(state=tk.DISABLED)
                
            # 只有URL和输出目录都有效时才能开始抓取
            if self.url_valid.get() and self.output_dir_valid.get():
                self.start_btn.configure(state=tk.NORMAL)
            else:
                self.start_btn.configure(state=tk.DISABLED)
                
            self.stop_btn.configure(state=tk.DISABLED)
            

        
    def _validate_url(self, *args):
        """验证URL格式和可达性"""
        url = self.url_var.get().strip()
        
        if not url:
            self.url_status_label.configure(text="⚪", foreground="gray")
            self.url_valid.set(False)
            return
            
        # 基础URL格式验证
        try:
            parsed = urlparse(url)
            if not all([parsed.scheme, parsed.netloc]):
                self.url_status_label.configure(text="✗", foreground="red")
                self.url_valid.set(False)
                return
        except Exception:
            self.url_status_label.configure(text="✗", foreground="red")
            self.url_valid.set(False)
            return
            
        # URL可达性检查（异步进行，避免阻塞UI）
        self.url_status_label.configure(text="⏳", foreground="orange")
        threading.Thread(target=self._check_url_reachability, args=(url,), daemon=True).start()
        
    def _check_url_reachability(self, url):
        """检查URL可达性（在后台线程中执行）"""
        try:
            response = requests.head(url, timeout=5, allow_redirects=True)
            if response.status_code < 400:
                self.root.after(0, lambda: self._set_url_valid(True))
            else:
                self.root.after(0, lambda: self._set_url_valid(False))
        except Exception:
            self.root.after(0, lambda: self._set_url_valid(False))
            
    def _set_url_valid(self, is_valid):
        """设置URL验证状态（在主线程中调用）"""
        if is_valid:
            self.url_status_label.configure(text="✓", foreground="green")
            self.url_valid.set(True)
        else:
            self.url_status_label.configure(text="✗", foreground="red")
            self.url_valid.set(False)
        # 更新按钮状态
        self._update_button_states()
    
    def _get_request_headers(self):
        """获取请求头配置"""
        headers = {
            'User-Agent': self.user_agent.get(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        return headers
    
    def _collect_scraping_tasks(self):
        """收集抓取任务"""
        tasks = []
        
        def collect_from_tree(item):
            values = self.preview_tree.item(item, "values")
            if len(values) >= 2:
                url = values[1]
                title = self.preview_tree.item(item, "text")
                tasks.append({
                    'url': url,
                    'title': title,
                    'tree_item': item
                })
            
            # 递归处理子项
            for child in self.preview_tree.get_children(item):
                collect_from_tree(child)
        
        # 从树的根节点开始收集
        for root_item in self.preview_tree.get_children():
            collect_from_tree(root_item)
            
        return tasks
    
    def _on_progress_update(self, progress_data):
        """处理进度更新回调"""
        self.root.after(0, lambda: self._update_progress_display(progress_data))
    
    def _update_progress_display(self, progress_data):
        """更新进度显示"""
        progress = progress_data.get('progress', 0)
        self.progress_var.set(progress)
        
        stats = progress_data.get('stats', {})
        self.success_var.set(f"成功: {stats.get('success', 0)}")
        self.failed_var.set(f"失败: {stats.get('failed', 0)}")
        self.skipped_var.set(f"跳过: {stats.get('skipped', 0)}")
    
    def _on_scraping_result(self, result_data):
        """处理抓取结果回调"""
        self.root.after(0, lambda: self._handle_scraping_result(result_data))
    
    def _handle_scraping_result(self, result_data):
        """处理抓取结果"""
        url = result_data.get('url')
        success = result_data.get('success', False)
        tree_item = result_data.get('tree_item')
        
        if tree_item:
            # 更新树形视图中的状态
            if success:
                self.preview_tree.set(tree_item, "status", "完成")
            else:
                self.preview_tree.set(tree_item, "status", "失败")
    
    def _on_scraping_error(self, error_type, error):
        """处理抓取错误回调"""
        error_msg = f"{error_type}: {str(error)}"
        self.root.after(0, lambda: self.logger.error(error_msg))
    
    def _on_scraping_completed(self):
        """抓取完成处理"""
        self.is_running = False
        self._update_button_states()
        self.status_var.set("抓取任务完成")
        self.logger.info("所有抓取任务已完成")
    
    def _set_status(self, status):
        """设置状态"""
        self.status_var.set(status)
        
    def run(self):
        """运行应用程序"""
        self.logger.info("技术文档抓取工具已启动")
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # 启动主循环
        self.root.mainloop()
        
    def _validate_output_dir(self, *args):
        """验证输出目录路径"""
        path = self.output_dir_var.get().strip()
        
        if not path:
            self.output_status_label.configure(text="✗", foreground="red")
            self.output_dir_valid.set(False)
            return
            
        try:
            path_obj = Path(path)
            # 检查父目录是否存在且可写
            if path_obj.exists():
                if path_obj.is_dir() and os.access(path, os.W_OK):
                    self.output_status_label.configure(text="✓", foreground="green")
                    self.output_dir_valid.set(True)
                else:
                    self.output_status_label.configure(text="✗", foreground="red")
                    self.output_dir_valid.set(False)
            else:
                # 检查是否可以创建目录
                parent = path_obj.parent
                if parent.exists() and os.access(parent, os.W_OK):
                    self.output_status_label.configure(text="✓", foreground="green")
                    self.output_dir_valid.set(True)
                else:
                    self.output_status_label.configure(text="✗", foreground="red")
                    self.output_dir_valid.set(False)
        except Exception:
            self.output_status_label.configure(text="✗", foreground="red")
            self.output_dir_valid.set(False)
            
        # 更新按钮状态
        self._update_button_states()
        
    def _count_tree_items(self):
        """计算树中的项目总数"""
        def count_children(item):
            count = 1  # 计算当前项目
            for child in self.preview_tree.get_children(item):
                count += count_children(child)
            return count
            
        total = 0
        for root_item in self.preview_tree.get_children():
            total += count_children(root_item)
        return total
        
    def _on_tree_double_click(self, event):
        """处理树形视图双击事件"""
        item = self.preview_tree.selection()[0] if self.preview_tree.selection() else None
        if item:
            # 获取URL
            values = self.preview_tree.item(item, "values")
            if len(values) >= 2:
                url = values[1]
                # 在浏览器中打开URL
                import webbrowser
                try:
                    webbrowser.open(url)
                    self.logger.info(f"在浏览器中打开: {url}")
                except Exception as e:
                     self.logger.error(f"无法打开URL {url}: {e}")
                     
    def _append_log(self, msg, level):
        """添加日志到文本框（带颜色编码）"""
        # 存储日志条目用于过滤
        log_entry = {
            'message': msg,
            'level': level,
            'timestamp': msg.split(' - ')[0] if ' - ' in msg else ''
        }
        self.all_log_entries.append(log_entry)
        
        # 应用当前过滤器
        self._refresh_log_display()
        
    def _refresh_log_display(self):
        """刷新日志显示（应用过滤器）"""
        # 清空显示
        self.log_text.delete(1.0, tk.END)
        
        # 获取过滤条件
        level_filter = self.log_level_var.get()
        search_text = self.log_search_var.get().lower()
        
        # 过滤并显示日志
        for entry in self.all_log_entries:
            # 级别过滤
            if level_filter != "全部" and entry['level'] != level_filter:
                continue
                
            # 搜索过滤
            if search_text and search_text not in entry['message'].lower():
                continue
                
            # 插入日志并应用颜色
            start_pos = self.log_text.index(tk.END)
            self.log_text.insert(tk.END, entry['message'] + "\n")
            end_pos = self.log_text.index(tk.END)
            
            # 应用颜色标签
            line_start = f"{start_pos.split('.')[0]}.0"
            line_end = f"{start_pos.split('.')[0]}.end"
            self.log_text.tag_add(entry['level'], line_start, line_end)
            
        # 滚动到底部
        self.log_text.see(tk.END)
        
    def _filter_logs(self, event=None):
        """过滤日志显示"""
        self._refresh_log_display()
        
    def _clear_logs(self):
        """清空所有日志"""
        self.all_log_entries.clear()
        self.log_text.delete(1.0, tk.END)
        self.logger.info("日志已清空")
              
    def _toggle_advanced_settings(self):
        """切换高级设置显示/隐藏"""
        if self.show_advanced.get():
            self.advanced_frame.grid()
        else:
            self.advanced_frame.grid_remove()
            
    def _on_closing(self):
        """窗口关闭事件处理"""
        if self.is_running:
            if messagebox.askokcancel("确认", "抓取任务正在进行中，确定要退出吗？"):
                self._stop_scraping()
                self._cleanup_resources()
                self.root.destroy()
        else:
            self._cleanup_resources()
            self.root.destroy()
    
    def _cleanup_resources(self):
        """清理资源"""
        try:
            # 停止任务管理器
            if hasattr(self, 'task_manager'):
                self.task_manager.stop(wait=False)
            
            # 关闭资源管理器
            if hasattr(self, 'resource_manager'):
                self.resource_manager.shutdown(timeout=5.0)
                
            self.logger.info("资源清理完成")
        except Exception as e:
            print(f"资源清理时发生错误: {e}")