#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
README生成器

负责自动生成目录索引文件，包含文档概述、目录结构、统计信息等。
提供完整的导航信息和快速访问链接。

作者: Assistant
创建时间: 2025-01-27
"""

from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import os

from .base import StorageConfig, StorageComponent, StorageError, DirectoryInfo, FileInfo


class ReadmeGenerator(StorageComponent):
    """README生成器"""
    
    def __init__(self):
        super().__init__("ReadmeGenerator")
    
    def process(self, root_dir: DirectoryInfo, config: StorageConfig, 
                source_url: Optional[str] = None) -> Path:
        """生成README文件
        
        Args:
            root_dir: 根目录信息
            config: 存储配置
            source_url: 源网站URL
            
        Returns:
            生成的README文件路径
        """
        readme_content = self._generate_readme_content(root_dir, config, source_url)
        readme_path = root_dir.path / "README.md"
        
        try:
            with open(readme_path, 'w', encoding=config.encoding) as f:
                f.write(readme_content)
            return readme_path
        except Exception as e:
            raise StorageError(f"生成README失败: {e}", "readme_generation", str(readme_path))
    
    def _generate_readme_content(self, root_dir: DirectoryInfo, config: StorageConfig,
                                source_url: Optional[str] = None) -> str:
        """生成README内容
        
        Args:
            root_dir: 根目录信息
            config: 存储配置
            source_url: 源网站URL
            
        Returns:
            README内容
        """
        content_parts = []
        
        # 标题和概述
        content_parts.append(self._generate_header(root_dir, source_url))
        
        # 统计信息
        content_parts.append(self._generate_statistics(root_dir))
        
        # 目录结构
        content_parts.append(self._generate_directory_tree(root_dir))
        
        # 文件列表
        content_parts.append(self._generate_file_list(root_dir))
        
        # 使用说明
        content_parts.append(self._generate_usage_guide())
        
        # 页脚信息
        content_parts.append(self._generate_footer())
        
        return "\n\n".join(content_parts)
    
    def _generate_header(self, root_dir: DirectoryInfo, source_url: Optional[str] = None) -> str:
        """生成头部信息
        
        Args:
            root_dir: 根目录信息
            source_url: 源网站URL
            
        Returns:
            头部内容
        """
        lines = []
        
        # 主标题
        title = root_dir.name if root_dir.name != "." else "技术文档"
        lines.append(f"# {title}")
        lines.append("")
        
        # 描述
        lines.append("本文档由技术文档抓取工具自动生成，包含完整的网站内容结构。")
        lines.append("")
        
        # 源信息
        if source_url:
            lines.append(f"**📖 原始网站**: [{source_url}]({source_url})")
        
        lines.append(f"**📅 生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"**📁 根目录**: `{root_dir.path.name}`")
        
        return "\n".join(lines)
    
    def _generate_statistics(self, root_dir: DirectoryInfo) -> str:
        """生成统计信息
        
        Args:
            root_dir: 根目录信息
            
        Returns:
            统计信息内容
        """
        # 更新统计信息
        root_dir.update_statistics()
        
        lines = []
        lines.append("## 📊 文档统计")
        lines.append("")
        
        # 基础统计
        lines.append(f"- **文件总数**: {root_dir.total_files} 个")
        lines.append(f"- **目录总数**: {self._count_directories(root_dir)} 个")
        
        # 大小统计
        if root_dir.total_size > 0:
            size_mb = root_dir.total_size / (1024 * 1024)
            if size_mb >= 1:
                lines.append(f"- **总大小**: {size_mb:.2f} MB")
            else:
                size_kb = root_dir.total_size / 1024
                lines.append(f"- **总大小**: {size_kb:.2f} KB")
        
        # 层级统计
        max_depth = self._get_max_depth(root_dir)
        lines.append(f"- **最大层级**: {max_depth} 层")
        
        return "\n".join(lines)
    
    def _generate_directory_tree(self, root_dir: DirectoryInfo) -> str:
        """生成目录树
        
        Args:
            root_dir: 根目录信息
            
        Returns:
            目录树内容
        """
        lines = []
        lines.append("## 📂 目录结构")
        lines.append("")
        lines.append("```")
        
        # 生成树状结构
        tree_lines = self._build_tree_lines(root_dir, "", True)
        lines.extend(tree_lines)
        
        lines.append("```")
        
        return "\n".join(lines)
    
    def _build_tree_lines(self, dir_info: DirectoryInfo, prefix: str, is_last: bool) -> List[str]:
        """构建树状结构行
        
        Args:
            dir_info: 目录信息
            prefix: 前缀字符
            is_last: 是否是最后一个
            
        Returns:
            树状结构行列表
        """
        lines = []
        
        # 当前目录
        connector = "└── " if is_last else "├── "
        dir_name = dir_info.name if dir_info.name != "." else "."
        lines.append(f"{prefix}{connector}{dir_name}/")
        
        # 子项目
        items = []
        
        # 添加子目录
        for subdir in dir_info.subdirs:
            items.append(('dir', subdir))
        
        # 添加文件
        for file_info in dir_info.files:
            items.append(('file', file_info))
        
        # 排序：目录在前，文件在后
        items.sort(key=lambda x: (x[0] == 'file', x[1].name if x[0] == 'dir' else x[1].filename))
        
        # 生成子项目的树状结构
        for i, (item_type, item) in enumerate(items):
            is_last_item = (i == len(items) - 1)
            new_prefix = prefix + ("    " if is_last else "│   ")
            
            if item_type == 'dir':
                lines.extend(self._build_tree_lines(item, new_prefix, is_last_item))
            else:
                connector = "└── " if is_last_item else "├── "
                filename = item.filename or "untitled.md"
                lines.append(f"{new_prefix}{connector}{filename}")
        
        return lines
    
    def _generate_file_list(self, root_dir: DirectoryInfo) -> str:
        """生成文件列表
        
        Args:
            root_dir: 根目录信息
            
        Returns:
            文件列表内容
        """
        lines = []
        lines.append("## 📄 文件导航")
        lines.append("")
        
        # 收集所有文件
        all_files = self._collect_all_files(root_dir)
        
        if not all_files:
            lines.append("*暂无文件*")
            return "\n".join(lines)
        
        # 按层级分组
        files_by_level = {}
        for file_info in all_files:
            level = file_info.level
            if level not in files_by_level:
                files_by_level[level] = []
            files_by_level[level].append(file_info)
        
        # 生成分层列表
        for level in sorted(files_by_level.keys()):
            if level == 0:
                lines.append("### 根目录文件")
            else:
                lines.append(f"### 第 {level} 层文件")
            lines.append("")
            
            files = files_by_level[level]
            files.sort(key=lambda f: f.index)
            
            for file_info in files:
                # 生成相对路径
                if file_info.relative_path:
                    file_path = str(file_info.relative_path).replace('\\', '/')
                else:
                    file_path = file_info.filename or "untitled.md"
                
                # 生成链接
                title = file_info.title or file_info.filename or "无标题"
                lines.append(f"- [{title}]({file_path})")
                
                # 添加源链接
                if file_info.url:
                    lines.append(f"  - 🔗 [原始链接]({file_info.url})")
            
            lines.append("")
        
        return "\n".join(lines)
    
    def _generate_usage_guide(self) -> str:
        """生成使用说明
        
        Returns:
            使用说明内容
        """
        lines = []
        lines.append("## 📖 使用说明")
        lines.append("")
        lines.append("### 文件结构")
        lines.append("")
        lines.append("- 每个Markdown文件都包含完整的元数据信息")
        lines.append("- 文件名采用序号前缀，便于按顺序浏览")
        lines.append("- 目录结构与原网站保持一致")
        lines.append("")
        lines.append("### 元数据说明")
        lines.append("")
        lines.append("每个文件头部包含以下信息：")
        lines.append("- `title`: 页面标题")
        lines.append("- `source_url`: 原始网页链接")
        lines.append("- `created_at`: 原始创建时间")
        lines.append("- `scraped_at`: 抓取时间")
        lines.append("- `level`: 目录层级")
        lines.append("- `index`: 同级序号")
        lines.append("")
        lines.append("### 导航方式")
        lines.append("")
        lines.append("1. **按目录浏览**: 根据文件夹结构逐级访问")
        lines.append("2. **按列表导航**: 使用本文件的文件导航部分")
        lines.append("3. **搜索功能**: 使用编辑器的搜索功能查找特定内容")
        
        return "\n".join(lines)
    
    def _generate_footer(self) -> str:
        """生成页脚信息
        
        Returns:
            页脚内容
        """
        lines = []
        lines.append("---")
        lines.append("")
        lines.append("## ℹ️ 生成信息")
        lines.append("")
        lines.append("- **工具名称**: 技术文档网站转Markdown工具")
        lines.append("- **工具版本**: 1.0.0")
        lines.append(f"- **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("- **编码格式**: UTF-8")
        lines.append("")
        lines.append("*本文档为自动生成，如有问题请检查原始网站内容。*")
        
        return "\n".join(lines)
    
    def _count_directories(self, dir_info: DirectoryInfo) -> int:
        """统计目录数量
        
        Args:
            dir_info: 目录信息
            
        Returns:
            目录数量
        """
        count = len(dir_info.subdirs)
        for subdir in dir_info.subdirs:
            count += self._count_directories(subdir)
        return count
    
    def _get_max_depth(self, dir_info: DirectoryInfo, current_depth: int = 0) -> int:
        """获取最大深度
        
        Args:
            dir_info: 目录信息
            current_depth: 当前深度
            
        Returns:
            最大深度
        """
        max_depth = current_depth
        
        for subdir in dir_info.subdirs:
            subdir_depth = self._get_max_depth(subdir, current_depth + 1)
            max_depth = max(max_depth, subdir_depth)
        
        return max_depth
    
    def _collect_all_files(self, dir_info: DirectoryInfo) -> List[FileInfo]:
        """收集所有文件
        
        Args:
            dir_info: 目录信息
            
        Returns:
            所有文件信息列表
        """
        all_files = []
        
        # 添加当前目录的文件
        all_files.extend(dir_info.files)
        
        # 递归添加子目录的文件
        for subdir in dir_info.subdirs:
            all_files.extend(self._collect_all_files(subdir))
        
        return all_files
    
    def generate_index_files(self, root_dir: DirectoryInfo, config: StorageConfig) -> List[Path]:
        """为每个目录生成索引文件
        
        Args:
            root_dir: 根目录信息
            config: 存储配置
            
        Returns:
            生成的索引文件路径列表
        """
        index_files = []
        
        # 为每个子目录生成索引
        for subdir in root_dir.subdirs:
            index_path = self._generate_directory_index(subdir, config)
            if index_path:
                index_files.append(index_path)
            
            # 递归处理子目录
            sub_index_files = self.generate_index_files(subdir, config)
            index_files.extend(sub_index_files)
        
        return index_files
    
    def _generate_directory_index(self, dir_info: DirectoryInfo, config: StorageConfig) -> Optional[Path]:
        """为单个目录生成索引文件
        
        Args:
            dir_info: 目录信息
            config: 存储配置
            
        Returns:
            生成的索引文件路径
        """
        if not dir_info.files and not dir_info.subdirs:
            return None
        
        index_content = self._generate_directory_index_content(dir_info)
        index_path = dir_info.path / "_index.md"
        
        try:
            with open(index_path, 'w', encoding=config.encoding) as f:
                f.write(index_content)
            return index_path
        except Exception:
            return None
    
    def _generate_directory_index_content(self, dir_info: DirectoryInfo) -> str:
        """生成目录索引内容
        
        Args:
            dir_info: 目录信息
            
        Returns:
            索引内容
        """
        lines = []
        
        # 标题
        lines.append(f"# {dir_info.name}")
        lines.append("")
        
        # 子目录
        if dir_info.subdirs:
            lines.append("## 📁 子目录")
            lines.append("")
            for subdir in dir_info.subdirs:
                lines.append(f"- [{subdir.name}](./{subdir.name}/README.md)")
            lines.append("")
        
        # 文件列表
        if dir_info.files:
            lines.append("## 📄 文件")
            lines.append("")
            for file_info in dir_info.files:
                title = file_info.title or file_info.filename or "无标题"
                filename = file_info.filename or "untitled.md"
                lines.append(f"- [{title}](./{filename})")
        
        return "\n".join(lines)