#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
存储管理器

作为存储模块的主要协调器，整合路径管理、文件组织、内容写入和README生成等组件，
完成从抓取结果到文件系统的完整存储流程。

作者: Assistant
创建时间: 2025-01-27
"""

from pathlib import Path
from typing import List, Optional, Dict, Any
import logging
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from .base import StorageConfig, StorageResult, StorageComponent, StorageError
from .path_manager import PathManager
from .file_organizer import FileOrganizer
from .content_writer import ContentWriter
from .readme_generator import ReadmeGenerator
from ..scraper.base import ScrapingResult


class StorageManager(StorageComponent):
    """存储管理器"""
    
    def __init__(self):
        super().__init__("StorageManager")
        self.path_manager = PathManager()
        self.file_organizer = FileOrganizer()
        self.content_writer = ContentWriter()
        self.readme_generator = ReadmeGenerator()
        
        # 设置日志记录器
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 线程安全锁
        self._lock = threading.Lock()
        
        # 统计信息
        self._stats = {
            'total_files': 0,
            'successful_files': 0,
            'failed_files': 0,
            'total_directories': 0,
            'processing_time': 0.0
        }
    
    def process(self, data, config: StorageConfig):
        """实现抽象方法process
        
        Args:
            data: 输入数据
            config: 存储配置
            
        Returns:
            处理结果
        """
        # 这个方法主要用于兼容抽象基类，实际使用store_content方法
        if isinstance(data, list):
            return self.store_content(data, config)
        else:
            raise StorageError(f"不支持的数据类型: {type(data)}")
    
    def store_content(self, scraping_results: List[ScrapingResult], 
                     config: StorageConfig, base_url: str = "",
                     menu_structure: Optional[List[Dict[str, Any]]] = None) -> StorageResult:
        """存储内容到文件系统
        
        Args:
            scraping_results: 抓取结果列表
            config: 存储配置
            base_url: 基础URL
            menu_structure: 菜单结构（可选）
            
        Returns:
            存储结果
        """
        start_time = time.time()
        
        try:
            # 验证配置（基本验证已在__post_init__中完成）
            if not config.output_dir:
                raise StorageError("输出目录不能为空")
            
            # 创建输出目录
            self._ensure_output_directory(config)
            
            # 过滤有效结果
            valid_results = [r for r in scraping_results if r.success and r.content]
            
            if not valid_results:
                raise StorageError("没有有效的抓取结果可以存储")
            
            self.logger.info(f"开始存储 {len(valid_results)} 个文件")
            
            # 组织文件结构
            if menu_structure:
                root_dir = self.file_organizer.organize_by_menu_structure(
                    valid_results, menu_structure, config
                )
            else:
                root_dir = self.file_organizer.process(valid_results, config, base_url)
            
            # 创建目录结构
            self._create_directory_structure(root_dir)
            
            # 写入文件内容
            write_results = self._write_files(root_dir, config)
            
            # 生成README文件
            readme_results = self._generate_readmes(root_dir, config)
            
            # 计算统计信息
            processing_time = time.time() - start_time
            
            # 创建存储结果
            storage_result = StorageResult(
                success=True,
                root_dir=root_dir,
                total_files=len(valid_results),
                processing_time=processing_time,
                message=f"成功存储 {len([r for r in write_results if r['success']])} 个文件"
            )
            
            # 设置文件统计
            storage_result.files_created = [r['file_path'] for r in write_results if r['success']]
            storage_result.total_dirs = self._count_directories(root_dir)
            
            # 添加统计信息
            storage_result.total_size = sum(len(r.content.encode('utf-8')) for r in valid_results)
            
            # 添加错误信息
            failed_writes = [r for r in write_results if not r['success']]
            for failed in failed_writes:
                storage_result.add_error(failed.get('error', '未知错误'))
            
            self.logger.info(f"存储完成，耗时 {processing_time:.2f}s")
            return storage_result
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"存储失败: {str(e)}"
            self.logger.error(error_msg)
            
            return StorageResult(
                success=False,
                message=error_msg,
                processing_time=processing_time
            )
    
    def _ensure_output_directory(self, config: StorageConfig):
        """确保输出目录存在
        
        Args:
            config: 存储配置
        """
        try:
            config.output_dir.mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"输出目录已创建: {config.output_dir}")
        except Exception as e:
            raise StorageError(f"无法创建输出目录 {config.output_dir}: {str(e)}")
    
    def _create_directory_structure(self, root_dir):
        """创建目录结构
        
        Args:
            root_dir: 根目录信息
        """
        def create_dirs(dir_info):
            try:
                dir_info.path.mkdir(parents=True, exist_ok=True)
                self.logger.debug(f"目录已创建: {dir_info.path}")
                
                # 递归创建子目录
                for subdir in dir_info.subdirs:
                    create_dirs(subdir)
                    
            except Exception as e:
                self.logger.error(f"创建目录失败 {dir_info.path}: {str(e)}")
                raise StorageError(f"创建目录失败: {str(e)}")
        
        create_dirs(root_dir)
    
    def _write_files(self, root_dir, config: StorageConfig) -> List[Dict[str, Any]]:
        """写入文件内容
        
        Args:
            root_dir: 根目录信息
            config: 存储配置
            
        Returns:
            写入结果列表
        """
        results = []
        
        # 收集所有文件
        all_files = self._collect_all_files(root_dir)
        
        if config.use_threading and len(all_files) > 1:
            # 并行写入
            results = self._write_files_parallel(all_files, config)
        else:
            # 串行写入
            results = self._write_files_sequential(all_files, config)
        
        return results
    
    def _collect_all_files(self, root_dir) -> List:
        """收集所有文件信息
        
        Args:
            root_dir: 根目录信息
            
        Returns:
            文件信息列表
        """
        files = []
        
        def collect_files(dir_info):
            files.extend(dir_info.files)
            for subdir in dir_info.subdirs:
                collect_files(subdir)
        
        collect_files(root_dir)
        return files
    
    def _write_files_sequential(self, files, config: StorageConfig) -> List[Dict[str, Any]]:
        """串行写入文件
        
        Args:
            files: 文件信息列表
            config: 存储配置
            
        Returns:
            写入结果列表
        """
        results = []
        
        for file_info in files:
            try:
                result = self.content_writer.process(file_info, config)
                results.append({
                    'file_path': file_info.absolute_path,
                    'success': True,
                    'result': result
                })
                self.logger.debug(f"文件写入成功: {file_info.filename}")
                
            except Exception as e:
                error_msg = f"写入文件失败 {file_info.filename}: {str(e)}"
                self.logger.error(error_msg)
                results.append({
                    'file_path': file_info.absolute_path,
                    'success': False,
                    'error': error_msg
                })
        
        return results
    
    def _write_files_parallel(self, files, config: StorageConfig) -> List[Dict[str, Any]]:
        """并行写入文件
        
        Args:
            files: 文件信息列表
            config: 存储配置
            
        Returns:
            写入结果列表
        """
        results = []
        max_workers = min(4, len(files))  # 默认最大4个线程
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_file = {
                executor.submit(self._write_single_file, file_info, config): file_info
                for file_info in files
            }
            
            # 收集结果
            for future in as_completed(future_to_file):
                file_info = future_to_file[future]
                try:
                    result = future.result()
                    results.append({
                        'file_path': file_info.absolute_path,
                        'success': True,
                        'result': result
                    })
                    self.logger.debug(f"文件写入成功: {file_info.filename}")
                    
                except Exception as e:
                    error_msg = f"写入文件失败 {file_info.filename}: {str(e)}"
                    self.logger.error(error_msg)
                    results.append({
                        'file_path': file_info.absolute_path,
                        'success': False,
                        'error': error_msg
                    })
        
        return results
    
    def _write_single_file(self, file_info, config: StorageConfig):
        """写入单个文件（线程安全）
        
        Args:
            file_info: 文件信息
            config: 存储配置
            
        Returns:
            写入结果
        """
        with self._lock:
            return self.content_writer.process(file_info, config)
    
    def _generate_readmes(self, root_dir, config: StorageConfig) -> List[Dict[str, Any]]:
        """生成README文件
        
        Args:
            root_dir: 根目录信息
            config: 存储配置
            
        Returns:
            README生成结果列表
        """
        results = []
        
        def generate_readme(dir_info):
            try:
                # 只为有内容的目录生成README
                if dir_info.files or dir_info.subdirs:
                    result = self.readme_generator.process(dir_info, config)
                    results.append({
                        'directory': dir_info.path,
                        'success': True,
                        'result': result
                    })
                    self.logger.debug(f"README生成成功: {dir_info.path}")
                
                # 递归处理子目录
                for subdir in dir_info.subdirs:
                    generate_readme(subdir)
                    
            except Exception as e:
                error_msg = f"生成README失败 {dir_info.path}: {str(e)}"
                self.logger.error(error_msg)
                results.append({
                    'directory': dir_info.path,
                    'success': False,
                    'error': error_msg
                })
        
        generate_readme(root_dir)
        return results
    
    def _count_directories(self, root_dir) -> int:
        """统计目录数量
        
        Args:
            root_dir: 根目录信息
            
        Returns:
            目录总数
        """
        count = 1  # 包含根目录
        
        def count_dirs(dir_info):
            nonlocal count
            count += len(dir_info.subdirs)
            for subdir in dir_info.subdirs:
                count_dirs(subdir)
        
        count_dirs(root_dir)
        return count
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            统计信息字典
        """
        with self._lock:
            return self._stats.copy()
    
    def clear_statistics(self):
        """清除统计信息"""
        with self._lock:
            self._stats = {
                'total_files': 0,
                'successful_files': 0,
                'failed_files': 0,
                'total_directories': 0,
                'processing_time': 0.0
            }
    
    def validate_storage_result(self, storage_result: StorageResult) -> bool:
        """验证存储结果
        
        Args:
            storage_result: 存储结果
            
        Returns:
            验证是否通过
        """
        if not storage_result.success:
            return False
        
        if not storage_result.root_directory:
            return False
        
        # 检查文件是否实际存在
        try:
            all_files = self._collect_all_files(storage_result.root_directory)
            for file_info in all_files:
                if not file_info.absolute_path.exists():
                    self.logger.warning(f"文件不存在: {file_info.absolute_path}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"验证存储结果失败: {str(e)}")
            return False
    
    def cleanup_failed_storage(self, config: StorageConfig):
        """清理失败的存储
        
        Args:
            config: 存储配置
        """
        try:
            if config.output_dir.exists():
                import shutil
                shutil.rmtree(config.output_dir)
                self.logger.info(f"已清理输出目录: {config.output_dir}")
        except Exception as e:
            self.logger.error(f"清理输出目录失败: {str(e)}")