#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
存储模块使用示例

展示如何使用文件组织与存储模块的各个组件。

作者: Assistant
创建时间: 2025-01-27
"""

from pathlib import Path
from typing import List

from ..storage.base import StorageConfig
from ..storage.storage_manager import StorageManager
from ..scraper.base import ScrapingResult


def create_sample_data() -> List[ScrapingResult]:
    """创建示例抓取数据"""
    return [
        ScrapingResult(
            url="https://docs.example.com/getting-started",
            title="快速开始",
            content="# 快速开始\n\n欢迎使用我们的产品！本指南将帮助您快速上手。\n\n## 安装\n\n请按照以下步骤进行安装...",
            success=True,
            content_length=80,
            internal_links=["https://docs.example.com/installation"],
            external_links=["https://github.com/example/repo"]
        ),
        ScrapingResult(
            url="https://docs.example.com/installation",
            title="安装指南",
            content="# 安装指南\n\n详细的安装步骤和系统要求。\n\n## 系统要求\n\n- Python 3.8+\n- Windows 10/11\n- 4GB RAM",
            success=True,
            content_length=75,
            internal_links=[],
            external_links=["https://python.org"]
        ),
        ScrapingResult(
            url="https://docs.example.com/api/overview",
            title="API 概览",
            content="# API 概览\n\n本文档介绍了主要的API接口和使用方法。\n\n## 认证\n\n所有API调用都需要有效的认证令牌...",
            success=True,
            content_length=90,
            internal_links=["https://docs.example.com/api/auth"],
            external_links=[]
        ),
        ScrapingResult(
            url="https://docs.example.com/api/auth",
            title="API 认证",
            content="# API 认证\n\n详细的认证流程和令牌管理说明。\n\n## 获取令牌\n\n通过以下方式获取访问令牌...",
            success=True,
            content_length=85,
            internal_links=[],
            external_links=[]
        ),
        ScrapingResult(
            url="https://docs.example.com/tutorials/basic",
            title="基础教程",
            content="# 基础教程\n\n从零开始学习如何使用我们的产品。\n\n## 第一步\n\n创建您的第一个项目...",
            success=True,
            content_length=70,
            internal_links=["https://docs.example.com/tutorials/advanced"],
            external_links=[]
        ),
        ScrapingResult(
            url="https://docs.example.com/tutorials/advanced",
            title="高级教程",
            content="# 高级教程\n\n深入了解产品的高级功能和最佳实践。\n\n## 性能优化\n\n如何优化您的应用性能...",
            success=True,
            content_length=95,
            internal_links=[],
            external_links=[]
        )
    ]


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 1. 创建存储配置
    config = StorageConfig(
        output_dir=Path("example_output"),
        add_index_prefix=True,
        add_metadata=True,
        generate_readme=True,
        max_filename_length=50
    )
    
    # 2. 创建存储管理器
    storage_manager = StorageManager()
    
    # 3. 准备抓取数据
    scraping_results = create_sample_data()
    
    # 4. 执行存储
    result = storage_manager.store_content(
        scraping_results=scraping_results,
        config=config,
        base_url="https://docs.example.com"
    )
    
    # 5. 检查结果
    if result.success:
        print(f"✅ 存储成功！")
        print(f"📁 输出目录: {config.output_dir}")
        print(f"📄 创建文件数: {len(result.files_created)}")
        print(f"📂 创建目录数: {result.total_dirs}")
        print(f"⏱️ 处理时间: {result.processing_time:.3f}s")
        print(f"💾 总大小: {result.total_size} 字节")
        
        # 显示生成的文件
        print("\n生成的文件:")
        for file_path in result.files_created:
            print(f"  📄 {file_path}")
            
    else:
        print(f"❌ 存储失败: {result.message}")
        if result.errors:
            print("错误详情:")
            for error in result.errors:
                print(f"  - {error}")


def example_with_menu_structure():
    """使用菜单结构的示例"""
    print("\n=== 菜单结构示例 ===")
    
    # 1. 定义菜单结构
    menu_structure = [
        {
            "title": "快速开始",
            "url": "https://docs.example.com/getting-started"
        },
        {
            "title": "安装指南", 
            "url": "https://docs.example.com/installation"
        },
        {
            "title": "API 文档",
            "children": [
                {
                    "title": "API 概览",
                    "url": "https://docs.example.com/api/overview"
                },
                {
                    "title": "API 认证",
                    "url": "https://docs.example.com/api/auth"
                }
            ]
        },
        {
            "title": "教程",
            "children": [
                {
                    "title": "基础教程",
                    "url": "https://docs.example.com/tutorials/basic"
                },
                {
                    "title": "高级教程",
                    "url": "https://docs.example.com/tutorials/advanced"
                }
            ]
        }
    ]
    
    # 2. 创建配置
    config = StorageConfig(
        output_dir=Path("menu_output"),
        add_index_prefix=True,
        add_metadata=True,
        generate_readme=True
    )
    
    # 3. 执行存储
    storage_manager = StorageManager()
    scraping_results = create_sample_data()
    
    # 使用菜单结构组织文件
    root_dir = storage_manager.file_organizer.organize_by_menu_structure(
        scraping_results, menu_structure, config
    )
    
    # 4. 创建目录并写入文件
    storage_manager._create_directory_structure(root_dir)
    write_results = storage_manager._write_files(root_dir, config)
    readme_results = storage_manager._generate_readmes(root_dir, config)
    
    print(f"✅ 按菜单结构组织完成！")
    print(f"📁 根目录: {root_dir.name}")
    print(f"📂 子目录数: {len(root_dir.subdirs)}")
    print(f"📄 文件数: {len(root_dir.files)}")
    
    # 显示目录结构
    def print_structure(dir_info, indent=0):
        prefix = "  " * indent
        print(f"{prefix}📁 {dir_info.name}/")
        for file_info in dir_info.files:
            print(f"{prefix}  📄 {file_info.filename}")
        for subdir in dir_info.subdirs:
            print_structure(subdir, indent + 1)
    
    print("\n目录结构:")
    print_structure(root_dir)


def example_custom_config():
    """自定义配置示例"""
    print("\n=== 自定义配置示例 ===")
    
    # 创建自定义配置
    config = StorageConfig(
        output_dir=Path("custom_output"),
        # 文件命名配置
        add_index_prefix=False,  # 不添加序号前缀
        sanitize_names=True,     # 清理文件名
        max_filename_length=30,  # 限制文件名长度
        
        # 内容配置
        add_metadata=False,      # 不添加元数据
        add_navigation=False,    # 不添加导航
        generate_readme=False,   # 不生成README
        
        # 安全配置
        atomic_write=True,       # 使用原子写入
        backup_existing=True,    # 备份已存在文件
        overwrite_existing=False # 不覆盖已存在文件
    )
    
    storage_manager = StorageManager()
    scraping_results = create_sample_data()[:2]  # 只使用前两个结果
    
    result = storage_manager.store_content(
        scraping_results, config, "https://docs.example.com"
    )
    
    if result.success:
        print(f"✅ 自定义配置存储成功！")
        print(f"📄 文件数: {len(result.files_created)}")
        print(f"⚙️ 配置特点: 无序号前缀、无元数据、无README")
    else:
        print(f"❌ 存储失败: {result.message}")


def main():
    """主函数"""
    print("存储模块使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_with_menu_structure()
        example_custom_config()
        
        print("\n🎉 所有示例运行完成！")
        print("\n💡 提示:")
        print("- 检查生成的输出目录查看结果")
        print("- 可以根据需要调整StorageConfig参数")
        print("- 支持自定义菜单结构组织文件")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()