#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
链接处理器

负责处理Markdown内容中的链接，包括内部链接转换和外部链接保留。

作者: Assistant
创建时间: 2025-07-30
"""

import re
from urllib.parse import urljoin, urlparse, urlunparse
from typing import List, Tuple, Optional, Dict
from pathlib import Path


class LinkProcessor:
    """链接处理器"""
    
    def __init__(self, base_url: str):
        """
        初始化链接处理器
        
        Args:
            base_url: 网站基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.parsed_base = urlparse(self.base_url)
        
        # 链接模式
        self.markdown_link_pattern = re.compile(r'\[([^\]]+)\]\(([^\)]+)\)')
        self.html_link_pattern = re.compile(r'<a[^>]+href=["\']([^"\'>]+)["\'][^>]*>([^<]+)</a>', re.IGNORECASE)
    
    def process_links(self, markdown: str, target_dir: Optional[Path] = None) -> Tuple[str, Dict[str, List[str]]]:
        """
        处理Markdown中的所有链接
        
        Args:
            markdown: Markdown内容
            target_dir: 目标目录路径（用于计算相对路径）
            
        Returns:
            Tuple[str, Dict]: 处理后的Markdown内容和链接统计信息
        """
        link_stats = {
            'internal': [],
            'external': [],
            'anchors': [],
            'converted': []
        }
        
        # 处理Markdown格式的链接
        processed_markdown = self._process_markdown_links(markdown, target_dir, link_stats)
        
        # 处理残留的HTML链接
        processed_markdown = self._process_html_links(processed_markdown, target_dir, link_stats)
        
        return processed_markdown, link_stats
    
    def _process_markdown_links(self, markdown: str, target_dir: Optional[Path], link_stats: Dict) -> str:
        """处理Markdown格式的链接"""
        def replace_link(match):
            text = match.group(1)
            url = match.group(2)
            
            # 处理链接
            processed_url, link_type = self._process_single_link(url, target_dir)
            
            # 记录统计信息
            self._record_link_stats(url, processed_url, link_type, link_stats)
            
            return f'[{text}]({processed_url})'
        
        return self.markdown_link_pattern.sub(replace_link, markdown)
    
    def _process_html_links(self, markdown: str, target_dir: Optional[Path], link_stats: Dict) -> str:
        """处理HTML格式的链接"""
        def replace_link(match):
            url = match.group(1)
            text = match.group(2)
            
            # 处理链接
            processed_url, link_type = self._process_single_link(url, target_dir)
            
            # 记录统计信息
            self._record_link_stats(url, processed_url, link_type, link_stats)
            
            return f'[{text}]({processed_url})'
        
        return self.html_link_pattern.sub(replace_link, markdown)
    
    def _process_single_link(self, url: str, target_dir: Optional[Path]) -> Tuple[str, str]:
        """
        处理单个链接
        
        Args:
            url: 原始URL
            target_dir: 目标目录
            
        Returns:
            Tuple[str, str]: (处理后的URL, 链接类型)
        """
        # 清理URL
        url = url.strip()
        
        # 处理锚点链接
        if url.startswith('#'):
            return url, 'anchor'
        
        # 解析URL
        parsed = urlparse(url)
        
        # 外部链接直接返回
        if parsed.netloc and parsed.netloc != self.parsed_base.netloc:
            return url, 'external'
        
        # 处理内部链接
        if self._is_internal_link(url):
            converted_url = self._convert_internal_link(url, target_dir)
            return converted_url, 'internal'
        
        # 相对链接转换为绝对链接后再处理
        if not parsed.netloc:
            absolute_url = urljoin(self.base_url, url)
            if self._is_internal_link(absolute_url):
                converted_url = self._convert_internal_link(absolute_url, target_dir)
                return converted_url, 'internal'
            else:
                return absolute_url, 'external'
        
        return url, 'unknown'
    
    def _is_internal_link(self, url: str) -> bool:
        """
        判断是否为内部链接
        
        Args:
            url: URL地址
            
        Returns:
            bool: 是否为内部链接
        """
        parsed = urlparse(url)
        
        # 没有域名的相对链接
        if not parsed.netloc:
            return True
        
        # 同域名链接
        if parsed.netloc == self.parsed_base.netloc:
            return True
        
        return False
    
    def _convert_internal_link(self, url: str, target_dir: Optional[Path]) -> str:
        """
        转换内部链接
        
        Args:
            url: 内部链接URL
            target_dir: 目标目录
            
        Returns:
            str: 转换后的链接
        """
        parsed = urlparse(url)
        path = parsed.path
        
        # 移除开头的斜杠
        if path.startswith('/'):
            path = path[1:]
        
        # 处理Starlight特有的文件扩展名转换
        path = self._convert_starlight_path(path)
        
        # 如果有目标目录，计算相对路径
        if target_dir:
            try:
                # 将URL路径转换为绝对文件系统路径
                url_path = Path.cwd() / path
                if path.endswith('.md'):
                    # 计算相对路径
                    relative_path = self._calculate_relative_path(target_dir, url_path)
                    path = str(relative_path).replace('\\', '/')  # 统一使用正斜杠
            except Exception:
                # 如果计算相对路径失败，保持原路径
                pass
        # 如果没有目标目录，直接返回转换后的路径（不添加前缀）
        
        # 保留锚点
        if parsed.fragment:
            path += f'#{parsed.fragment}'
        
        return path
    
    def _convert_starlight_path(self, path: str) -> str:
        """
        转换Starlight特有的路径格式
        
        Args:
            path: 原始路径
            
        Returns:
            str: 转换后的路径
        """
        # 移除尾部斜杠
        path = path.rstrip('/')
        
        # 如果路径为空或只是根路径，转换为index.md
        if not path or path == '/':
            return 'index.md'
        
        # 如果路径以.astro结尾，转换为.md
        if path.endswith('.astro'):
            path = path[:-6] + '.md'
        
        # 如果路径没有扩展名，添加.md
        if '.' not in Path(path).name:
            path += '.md'
        
        return path
    
    def _calculate_relative_path(self, from_dir: Path, to_path: Path) -> Path:
        """
        计算相对路径
        
        Args:
            from_dir: 源目录
            to_path: 目标路径
            
        Returns:
            Path: 相对路径
        """
        try:
            # 确保都是绝对路径
            if not from_dir.is_absolute():
                from_dir = Path.cwd() / from_dir
            if not to_path.is_absolute():
                to_path = Path.cwd() / to_path
            
            # 计算相对路径 - 使用os.path.relpath的逻辑
            import os
            relative_path = os.path.relpath(str(to_path), str(from_dir))
            return Path(relative_path)
        except (ValueError, OSError):
            # 如果无法计算相对路径，返回原路径
            return to_path
    
    def _record_link_stats(self, original_url: str, processed_url: str, link_type: str, link_stats: Dict):
        """
        记录链接统计信息
        
        Args:
            original_url: 原始URL
            processed_url: 处理后的URL
            link_type: 链接类型
            link_stats: 统计信息字典
        """
        if link_type == 'internal':
            link_stats['internal'].append(original_url)
            if original_url != processed_url:
                link_stats['converted'].append({
                    'original': original_url,
                    'converted': processed_url
                })
        elif link_type == 'external':
            link_stats['external'].append(original_url)
        elif link_type == 'anchor':
            link_stats['anchors'].append(original_url)
    
    def extract_links_from_markdown(self, markdown: str) -> List[str]:
        """
        从Markdown中提取所有链接
        
        Args:
            markdown: Markdown内容
            
        Returns:
            List[str]: 链接列表
        """
        links = []
        
        # 提取Markdown格式的链接
        for match in self.markdown_link_pattern.finditer(markdown):
            links.append(match.group(2))
        
        # 提取HTML格式的链接
        for match in self.html_link_pattern.finditer(markdown):
            links.append(match.group(1))
        
        return links
    
    def validate_links(self, links: List[str]) -> Dict[str, List[str]]:
        """
        验证链接有效性
        
        Args:
            links: 链接列表
            
        Returns:
            Dict[str, List[str]]: 分类后的链接
        """
        result = {
            'valid_internal': [],
            'valid_external': [],
            'invalid': [],
            'anchors': []
        }
        
        for link in links:
            if link.startswith('#'):
                result['anchors'].append(link)
            elif self._is_internal_link(link):
                result['valid_internal'].append(link)
            else:
                try:
                    parsed = urlparse(link)
                    if parsed.netloc:
                        result['valid_external'].append(link)
                    else:
                        result['invalid'].append(link)
                except Exception:
                    result['invalid'].append(link)
        
        return result