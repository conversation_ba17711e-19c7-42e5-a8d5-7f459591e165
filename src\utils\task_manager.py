#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务管理模块

本模块提供任务状态管理、任务控制和任务队列管理功能。
"""

import time
import threading
import logging
from typing import Dict, List, Optional, Callable, Any
from enum import Enum
from dataclasses import dataclass, field
from queue import Queue, Empty
import uuid


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 未开始
    RUNNING = "running"      # 进行中
    PAUSED = "paused"        # 已暂停
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消


@dataclass
class TaskResult:
    """任务执行结果"""
    success: bool = False
    result: Any = None
    error: Optional[Exception] = None
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    duration: Optional[float] = None
    
    def finish(self, success: bool, result: Any = None, error: Optional[Exception] = None):
        """标记任务完成"""
        self.success = success
        self.result = result
        self.error = error
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time


@dataclass
class Task:
    """任务对象"""
    id: str
    name: str
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: int = 0
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[TaskResult] = None
    created_time: float = field(default_factory=time.time)
    started_time: Optional[float] = None
    metadata: dict = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.result:
            self.result = TaskResult()
    
    def start(self):
        """开始执行任务"""
        self.status = TaskStatus.RUNNING
        self.started_time = time.time()
        self.result.start_time = self.started_time
    
    def complete(self, success: bool, result: Any = None, error: Optional[Exception] = None):
        """完成任务"""
        self.status = TaskStatus.COMPLETED if success else TaskStatus.FAILED
        self.result.finish(success, result, error)
    
    def cancel(self):
        """取消任务"""
        self.status = TaskStatus.CANCELLED
    
    def pause(self):
        """暂停任务"""
        if self.status == TaskStatus.RUNNING:
            self.status = TaskStatus.PAUSED
    
    def resume(self):
        """恢复任务"""
        if self.status == TaskStatus.PAUSED:
            self.status = TaskStatus.RUNNING


class TaskManager:
    """任务管理器"""
    
    def __init__(self, max_workers: int = 5, logger: Optional[logging.Logger] = None):
        """
        初始化任务管理器
        
        Args:
            max_workers: 最大工作线程数
            logger: 日志记录器
        """
        self.max_workers = max_workers
        self.logger = logger or logging.getLogger(__name__)
        
        # 任务存储
        self.tasks: Dict[str, Task] = {}
        self.task_queue = Queue()
        
        # 状态管理
        self._running = False
        self._paused = False
        self._workers: List[threading.Thread] = []
        self._lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total': 0,
            'completed': 0,
            'failed': 0,
            'cancelled': 0,
            'running': 0
        }
        
        # 回调函数
        self.callbacks = {
            'task_started': [],
            'task_completed': [],
            'task_failed': [],
            'progress_updated': []
        }
    
    def add_callback(self, event: str, callback: Callable):
        """添加回调函数"""
        if event in self.callbacks:
            self.callbacks[event].append(callback)
    
    def remove_callback(self, event: str, callback: Callable):
        """移除回调函数"""
        if event in self.callbacks and callback in self.callbacks[event]:
            self.callbacks[event].remove(callback)
    
    def _trigger_callback(self, event: str, *args, **kwargs):
        """触发回调函数"""
        for callback in self.callbacks.get(event, []):
            try:
                callback(*args, **kwargs)
            except Exception as e:
                self.logger.error(f"回调函数执行失败: {e}")
    
    def add_task(self, name: str, func: Callable, *args, priority: int = 0, 
                 task_id: Optional[str] = None, **kwargs) -> str:
        """添加任务"""
        task = Task(
            id=task_id or str(uuid.uuid4()),
            name=name,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority
        )
        
        with self._lock:
            self.tasks[task.id] = task
            self.task_queue.put(task)
            self.stats['total'] += 1
        
        self.logger.info(f"添加任务: {task.name} (ID: {task.id})")
        return task.id
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task = self.get_task(task_id)
        if task and task.status in [TaskStatus.PENDING, TaskStatus.PAUSED]:
            task.cancel()
            with self._lock:
                self.stats['cancelled'] += 1
            self.logger.info(f"取消任务: {task.name} (ID: {task_id})")
            return True
        return False
    
    def pause_all(self):
        """暂停所有任务"""
        with self._lock:
            self._paused = True
        self.logger.info("暂停所有任务")
    
    def resume_all(self):
        """恢复所有任务"""
        with self._lock:
            self._paused = False
        self.logger.info("恢复所有任务")
    
    def start(self):
        """启动任务管理器"""
        if self._running:
            return
        
        with self._lock:
            self._running = True
            self._paused = False
        
        # 启动工作线程
        for i in range(self.max_workers):
            worker = threading.Thread(target=self._worker, name=f"TaskWorker-{i}")
            worker.daemon = True
            worker.start()
            self._workers.append(worker)
        
        self.logger.info(f"任务管理器启动，工作线程数: {self.max_workers}")
    
    def stop(self, wait: bool = True):
        """停止任务管理器"""
        with self._lock:
            self._running = False
        
        # 取消所有待处理任务
        for task in self.tasks.values():
            if task.status == TaskStatus.PENDING:
                task.cancel()
        
        if wait:
            # 等待工作线程结束
            for worker in self._workers:
                if worker.is_alive():
                    worker.join(timeout=5.0)
        
        self._workers.clear()
        self.logger.info("任务管理器已停止")
    
    def _worker(self):
        """工作线程主循环"""
        while self._running:
            try:
                # 检查是否暂停
                if self._paused:
                    time.sleep(0.1)
                    continue
                
                # 获取任务
                try:
                    task = self.task_queue.get(timeout=1.0)
                except Empty:
                    continue
                
                # 检查任务状态
                if task.status != TaskStatus.PENDING:
                    continue
                
                # 执行任务
                self._execute_task(task)
                
            except Exception as e:
                self.logger.error(f"工作线程异常: {e}")
    
    def _execute_task(self, task: Task):
        """执行单个任务"""
        try:
            # 开始任务
            task.start()
            with self._lock:
                self.stats['running'] += 1
            
            self.logger.info(f"开始执行任务: {task.name} (ID: {task.id})")
            self._trigger_callback('task_started', task)
            
            # 执行任务函数
            result = task.func(*task.args, **task.kwargs)
            
            # 完成任务
            task.complete(True, result)
            with self._lock:
                self.stats['running'] -= 1
                self.stats['completed'] += 1
            
            self.logger.info(f"任务执行成功: {task.name} (ID: {task.id})")
            self._trigger_callback('task_completed', task)
            
        except Exception as e:
            # 任务失败
            task.complete(False, error=e)
            with self._lock:
                self.stats['running'] -= 1
                self.stats['failed'] += 1
            
            self.logger.error(f"任务执行失败: {task.name} (ID: {task.id}), 错误: {e}")
            self._trigger_callback('task_failed', task, e)
        
        finally:
            # 更新进度
            self._trigger_callback('progress_updated', self.get_progress())
    
    def get_progress(self) -> dict:
        """获取进度信息"""
        with self._lock:
            total = self.stats['total']
            completed = self.stats['completed'] + self.stats['failed'] + self.stats['cancelled']
            
            return {
                'total': total,
                'completed': completed,
                'success': self.stats['completed'],
                'failed': self.stats['failed'],
                'cancelled': self.stats['cancelled'],
                'running': self.stats['running'],
                'pending': total - completed - self.stats['running'],
                'progress_percent': (completed / total * 100) if total > 0 else 0
            }
    
    def get_status(self) -> dict:
        """获取管理器状态"""
        return {
            'running': self._running,
            'paused': self._paused,
            'worker_count': len(self._workers),
            'task_count': len(self.tasks),
            'queue_size': self.task_queue.qsize(),
            'stats': self.stats.copy()
        }
    
    def clear_completed(self):
        """清理已完成的任务"""
        with self._lock:
            completed_tasks = [
                task_id for task_id, task in self.tasks.items()
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
            ]
            
            for task_id in completed_tasks:
                del self.tasks[task_id]
        
        self.logger.info(f"清理了 {len(completed_tasks)} 个已完成任务")
    
    def reset(self):
        """重置任务管理器"""
        self.stop(wait=True)
        
        with self._lock:
            self.tasks.clear()
            # 清空队列
            while not self.task_queue.empty():
                try:
                    self.task_queue.get_nowait()
                except Empty:
                    break
            
            # 重置统计
            self.stats = {
                'total': 0,
                'completed': 0,
                'failed': 0,
                'cancelled': 0,
                'running': 0
            }
        
        self.logger.info("任务管理器已重置")