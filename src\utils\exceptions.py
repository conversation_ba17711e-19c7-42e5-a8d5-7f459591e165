#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异常处理和重试机制模块

本模块定义了系统中使用的自定义异常类和重试机制，提供统一的错误处理和恢复策略。
"""

import time
import logging
from typing import Callable, Any, Optional, Type, Union
from enum import Enum


class ExceptionType(Enum):
    """异常类型枚举"""
    NETWORK = "network"
    PARSE = "parse"
    FILE = "file"
    SYSTEM = "system"
    UNKNOWN = "unknown"


class ScrapingException(Exception):
    """抓取系统基础异常类"""
    
    def __init__(self, message: str, exception_type: ExceptionType = ExceptionType.UNKNOWN, 
                 original_exception: Optional[Exception] = None):
        super().__init__(message)
        self.exception_type = exception_type
        self.original_exception = original_exception
        self.timestamp = time.time()
    
    def __str__(self):
        return f"[{self.exception_type.value.upper()}] {super().__str__()}"


class NetworkException(ScrapingException):
    """网络相关异常"""
    
    def __init__(self, message: str, url: Optional[str] = None, 
                 status_code: Optional[int] = None, original_exception: Optional[Exception] = None):
        super().__init__(message, ExceptionType.NETWORK, original_exception)
        self.url = url
        self.status_code = status_code


class ParseException(ScrapingException):
    """解析相关异常"""
    
    def __init__(self, message: str, url: Optional[str] = None, 
                 element: Optional[str] = None, original_exception: Optional[Exception] = None):
        super().__init__(message, ExceptionType.PARSE, original_exception)
        self.url = url
        self.element = element


class FileException(ScrapingException):
    """文件操作相关异常"""
    
    def __init__(self, message: str, file_path: Optional[str] = None, 
                 operation: Optional[str] = None, original_exception: Optional[Exception] = None):
        super().__init__(message, ExceptionType.FILE, original_exception)
        self.file_path = file_path
        self.operation = operation


class SystemException(ScrapingException):
    """系统相关异常"""
    
    def __init__(self, message: str, resource_type: Optional[str] = None, 
                 original_exception: Optional[Exception] = None):
        super().__init__(message, ExceptionType.SYSTEM, original_exception)
        self.resource_type = resource_type


class RetryManager:
    """重试管理器"""
    
    def __init__(self, max_retries: int = 1, retry_delay: float = 1.0, 
                 backoff_factor: float = 1.0, logger: Optional[logging.Logger] = None):
        """
        初始化重试管理器
        
        Args:
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间（秒）
            backoff_factor: 退避因子（每次重试延迟乘以此因子）
            logger: 日志记录器
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.backoff_factor = backoff_factor
        self.logger = logger or logging.getLogger(__name__)
    
    def should_retry(self, exception: Exception, attempt: int) -> bool:
        """
        判断是否应该重试
        
        Args:
            exception: 发生的异常
            attempt: 当前尝试次数
            
        Returns:
            是否应该重试
        """
        if attempt >= self.max_retries:
            return False
        
        # 网络异常通常可以重试
        if isinstance(exception, (NetworkException, ConnectionError, TimeoutError)):
            return True
        
        # 某些HTTP状态码可以重试
        if isinstance(exception, NetworkException) and exception.status_code:
            # 5xx服务器错误和429限流可以重试
            if exception.status_code >= 500 or exception.status_code == 429:
                return True
        
        # 其他异常通常不重试
        return False
    
    def get_delay(self, attempt: int) -> float:
        """
        获取重试延迟时间
        
        Args:
            attempt: 当前尝试次数
            
        Returns:
            延迟时间（秒）
        """
        return self.retry_delay * (self.backoff_factor ** attempt)
    
    def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """
        执行函数并在失败时重试
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            最后一次执行的异常
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if not self.should_retry(e, attempt):
                    self.logger.error(f"执行失败，不进行重试: {e}")
                    break
                
                if attempt < self.max_retries:
                    delay = self.get_delay(attempt)
                    self.logger.warning(f"执行失败，{delay}秒后进行第{attempt + 1}次重试: {e}")
                    time.sleep(delay)
                else:
                    self.logger.error(f"重试{self.max_retries}次后仍然失败: {e}")
        
        # 重新抛出最后一次的异常
        if last_exception:
            raise last_exception


def handle_exception(exception: Exception, context: str = "") -> ScrapingException:
    """
    将标准异常转换为自定义异常
    
    Args:
        exception: 原始异常
        context: 异常上下文信息
        
    Returns:
        转换后的自定义异常
    """
    message = f"{context}: {str(exception)}" if context else str(exception)
    
    # 网络相关异常
    if isinstance(exception, (ConnectionError, TimeoutError)):
        return NetworkException(message, original_exception=exception)
    
    # 文件相关异常
    if isinstance(exception, (FileNotFoundError, PermissionError, OSError)):
        return FileException(message, original_exception=exception)
    
    # 内存相关异常
    if isinstance(exception, MemoryError):
        return SystemException(message, resource_type="memory", original_exception=exception)
    
    # 解析相关异常
    if isinstance(exception, (ValueError, AttributeError, KeyError)):
        return ParseException(message, original_exception=exception)
    
    # 其他异常
    return ScrapingException(message, original_exception=exception)


def safe_execute(func: Callable, *args, logger: Optional[logging.Logger] = None, 
                context: str = "", **kwargs) -> tuple[bool, Any, Optional[ScrapingException]]:
    """
    安全执行函数，捕获并处理异常
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        logger: 日志记录器
        context: 执行上下文
        **kwargs: 函数关键字参数
        
    Returns:
        (是否成功, 执行结果, 异常信息)
    """
    try:
        result = func(*args, **kwargs)
        return True, result, None
    except Exception as e:
        scraping_exception = handle_exception(e, context)
        if logger:
            logger.error(f"执行失败: {scraping_exception}")
        return False, None, scraping_exception