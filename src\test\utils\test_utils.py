#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
06-基础功能模块测试脚本

用于验证基础功能模块的各个组件是否正常工作。
"""

import sys
import os
import time
import threading
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from utils import (
        # 异常处理
        ScrapingException, NetworkException, RetryManager, safe_execute,
        # 任务管理
        TaskManager, TaskStatus,
        # 编码处理
        detect_encoding, safe_decode, clean_filename,
        # 日志系统
        setup_logger, get_logger, LogLevel,
        # 配置管理
        get_config_manager, get_config, set_config,
        # 资源管理
        get_resource_manager, get_memory_usage, submit_background_task
    )
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有依赖包：pip install -r requirements.txt")
    sys.exit(1)


def test_exceptions():
    """测试异常处理模块"""
    print("\n=== 测试异常处理模块 ===")
    
    # 测试自定义异常
    try:
        raise NetworkException("网络连接失败", url='http://example.com', status_code=404)
    except ScrapingException as e:
        print(f"✓ 捕获异常: {e}")
        print(f"  异常类型: {e.exception_type}")
        if hasattr(e, 'url'):
            print(f"  URL: {e.url}")
        if hasattr(e, 'status_code'):
            print(f"  状态码: {e.status_code}")
    
    # 测试重试管理器
    retry_manager = RetryManager(max_retries=3, retry_delay=0.1)
    
    def failing_function():
        if retry_manager.current_attempt < 2:
            raise NetworkException("模拟网络错误")
        return "成功!"
    
    result = safe_execute(failing_function, retry_manager=retry_manager)
    print(f"✓ 重试机制测试: {result}")


def test_task_manager():
    """测试任务管理模块"""
    print("\n=== 测试任务管理模块 ===")
    
    task_manager = TaskManager(max_workers=2)
    
    def sample_task(task_id, duration=1):
        print(f"  任务 {task_id} 开始执行")
        time.sleep(duration)
        print(f"  任务 {task_id} 执行完成")
        return f"任务 {task_id} 结果"
    
    # 添加任务
    task1_id = task_manager.add_task("Task-1", sample_task, 0.5)
    task2_id = task_manager.add_task("Task-2", sample_task, 0.3)
    
    print(f"✓ 添加了 {task_manager.stats['total']} 个任务")
    
    # 启动任务管理器
    task_manager.start()
    
    # 等待任务完成
    time.sleep(2)
    
    # 获取结果
    task1 = task_manager.get_task(task1_id)
    task2 = task_manager.get_task(task2_id)
    
    if task1 and task1.result:
        print(f"✓ 任务1结果: {task1.result.result}")
    if task2 and task2.result:
        print(f"✓ 任务2结果: {task2.result.result}")
    
    # 停止任务管理器
    task_manager.stop()
    print("✓ 任务管理器已停止")


def test_encoding_utils():
    """测试编码处理模块"""
    print("\n=== 测试编码处理模块 ===")
    
    # 测试编码检测
    test_text = "这是一个测试文本"
    test_bytes = test_text.encode('utf-8')
    
    detected = detect_encoding(test_bytes)
    print(f"✓ 编码检测: {detected}")
    
    # 测试安全解码
    decoded = safe_decode(test_bytes)
    print(f"✓ 安全解码: {decoded}")
    
    # 测试文件名清理
    dirty_filename = "测试文件<>:\"/|?*.txt"
    clean_name = clean_filename(dirty_filename)
    print(f"✓ 文件名清理: '{dirty_filename}' -> '{clean_name}'")


def test_logger():
    """测试日志系统"""
    print("\n=== 测试日志系统 ===")
    
    # 设置日志
    logger = setup_logger(
        name='test_logger',
        level=LogLevel.DEBUG,
        console_output=True
    )
    
    # 测试各级别日志
    logger.debug("这是调试信息")
    logger.info("这是普通信息")
    logger.warning("这是警告信息")
    logger.error("这是错误信息")
    
    print("✓ 日志系统测试完成")


def test_config_manager():
    """测试配置管理模块"""
    print("\n=== 测试配置管理模块 ===")
    
    config_manager = get_config_manager()
    
    # 测试获取默认配置
    max_workers = get_config('max_workers')
    print(f"✓ 默认最大工作线程数: {max_workers}")
    
    # 测试设置配置
    set_config('timeout', 60)
    timeout = get_config('timeout')
    print(f"✓ 设置超时时间: {timeout}")
    
    # 测试配置验证
    try:
        set_config('max_workers', 0)  # 应该失败
    except ValueError as e:
        print(f"✓ 配置验证成功: {e}")
    
    print("✓ 配置管理测试完成")


def test_resource_manager():
    """测试资源管理模块"""
    print("\n=== 测试资源管理模块 ===")
    
    resource_manager = get_resource_manager()
    
    # 测试内存监控
    memory_stats = get_memory_usage()
    print(f"✓ 内存使用情况:")
    print(f"  总内存: {memory_stats.total_memory / 1024 / 1024:.1f} MB")
    print(f"  已用内存: {memory_stats.used_memory / 1024 / 1024:.1f} MB")
    print(f"  内存使用率: {memory_stats.memory_percent:.1f}%")
    
    # 测试后台任务
    def background_task(name):
        time.sleep(0.5)
        return f"后台任务 {name} 完成"
    
    future = submit_background_task(background_task, "测试任务")
    result = future.result(timeout=2)
    print(f"✓ 后台任务结果: {result}")
    
    # 测试系统统计
    stats = resource_manager.get_system_stats()
    print(f"✓ 系统统计: 线程池最大工作线程数 {stats['thread_pool']['max_workers']}")
    
    print("✓ 资源管理测试完成")


def main():
    """主测试函数"""
    print("开始测试 06-基础功能模块...")
    print("=" * 50)
    
    try:
        test_exceptions()
        test_encoding_utils()
        test_logger()
        test_config_manager()
        test_resource_manager()
        test_task_manager()  # 放在最后，因为涉及多线程
        
        print("\n" + "=" * 50)
        print("✅ 所有测试通过！06-基础功能模块工作正常。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)