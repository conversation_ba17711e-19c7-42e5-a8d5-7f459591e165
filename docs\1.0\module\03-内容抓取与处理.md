# 内容抓取与处理模块

## 模块概述
本模块负责抓取网页内容并转换为Markdown格式，针对Astro+Starlight框架进行专门优化。

## 功能要求

### 1. Starlight内容提取
- **主要内容区域识别**：
  - `.sl-markdown-content`：Starlight主要内容容器
  - `main`：HTML5语义化主内容区域
  - `article`：文章内容区域
  - 其他内容容器的备选方案

- **内容过滤**：
  - 过滤Starlight特有导航元素：`.header`、`.sidebar`
  - 过滤分页组件：`.pagination`、`.edit-page`
  - 过滤广告和无关内容
  - 保留核心文档内容

- **特殊元素保留**：
  - 代码块：`<pre><code>`、`.expressive-code`
  - Starlight组件：`.sl-badge`、`.aside`、`.steps`
  - 表格、列表、引用等结构化内容
  - 内联代码和强调文本

### 2. Markdown转换
- **HTML到Markdown转换**：
  - 使用html2text进行基础转换
  - 保持Starlight特有格式（代码高亮、提示框、徽章等）
  - 处理特殊字符转义和中文内容
  - 保持标题层级结构（H1-H6）

- **格式优化**：
  - 代码块语言标识保留
  - 表格格式规范化
  - 列表缩进和编号处理
  - 链接和图片引用格式化

### 3. 图片处理
- **第一阶段策略**：
  - 暂不下载图片到本地
  - 保留原始图片链接
  - 确保图片链接的有效性
  - 为后续图片下载功能预留接口

### 4. 链接处理
- **内部链接转换**：
  - Starlight内部链接转换为相对路径
  - 处理`.astro`文件链接转换为`.md`
  - 维护文档间的引用关系
  - 确保链接在本地环境中可用

- **外部链接处理**：
  - 外部链接保持原样
  - 正确处理锚点链接和页面内跳转
  - URL编码和特殊字符处理

## 技术实现

### 1. 核心依赖库
- **网络请求**：requests（处理HTTP请求）
- **HTML解析**：BeautifulSoup4 + lxml（DOM解析和操作）
- **Markdown转换**：html2text（轻量级，适合Starlight内容）

### 2. 抓取策略
- **多线程抓取**：
  - 线程池管理（1-5个线程）
  - 请求频率控制（0.5-3秒间隔）
  - 并发安全的数据结构

- **错误处理**：
  - 网络超时重试机制（重试1次）
  - HTTP状态码处理
  - 内容解析异常处理
  - 优雅降级策略

### 3. 内容处理流程
1. **页面抓取**：发送HTTP请求获取HTML内容
2. **内容提取**：使用CSS选择器提取主要内容
3. **清理过滤**：移除导航、广告等无关元素
4. **格式转换**：HTML转换为Markdown格式
5. **链接处理**：转换内部链接，保留外部链接
6. **质量检查**：验证转换结果的完整性

## 质量保证

### 1. 内容完整性
- **提取准确性**：确保核心内容不丢失
- **格式保持**：保留原文档的结构和样式
- **特殊元素**：正确处理代码块、表格等

### 2. 性能优化
- **内存管理**：及时释放大型HTML文档
- **并发控制**：合理的线程数量和请求频率
- **缓存策略**：避免重复抓取相同内容

### 3. 错误处理
- **网络异常**：超时、连接失败等情况的处理
- **解析错误**：HTML结构异常的容错处理
- **编码问题**：字符编码检测和转换

### 4. 扩展性
- **插件接口**：为其他框架的内容提取预留扩展点
- **配置化**：提取规则和转换参数可配置
- **模块化**：独立的抓取器和转换器组件