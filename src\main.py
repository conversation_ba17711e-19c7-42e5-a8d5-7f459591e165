#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术文档网站转markdown工具 - 主程序入口

这是一个基于Python和tkinter的桌面应用，用于抓取技术文档网站的内容并转换为markdown文件。
第一阶段专门支持Astro+Starlight框架。
"""

import tkinter as tk
from tkinter import ttk
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent
sys.path.insert(0, str(src_path))

from core.app import DocumentScraperApp

def main():
    """主程序入口点"""
    try:
        # 创建主应用程序
        app = DocumentScraperApp()
        
        # 启动GUI主循环
        app.run()
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()