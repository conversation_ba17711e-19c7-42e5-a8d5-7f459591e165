## 背景
我想实现一个桌面应用，可以根据我输入的技术文档的网址，自动把该网址对应页面左侧菜单下各个页面的内容都抓取下来保存到本地markdown文件中。

## 核心功能要求

### 1. 基础架构
- 用Python实现的桌面软件，使用tkinter作为GUI框架
- 采用多线程架构，确保UI响应性和抓取效率
- 支持预览、开始、停止等操作

### 2. 网站识别与解析（优先支持Astro+Starlight）
- **框架识别**：
  - **第一阶段**：专门支持Astro+Starlight框架
  - 通过检测特征元素识别：`class="astro-*"`、`window.StarlightThemeProvider`、`.sidebar-sublist-wrapper`等
  - **扩展设计**：采用插件化架构，后续可轻松添加其他框架支持
- **Astro+Starlight菜单解析**：
  - 解析`.sidebar-sublist-wrapper ul li`结构获取菜单项
  - 识别`<details>`和`<summary>`标签处理折叠菜单
  - 提取`aria-current="page"`识别当前页面
  - 解析嵌套的`ul`标签获取子菜单层级
  - 支持多级嵌套菜单（最多5级深度）
- **链接提取与处理**：
  - 从`<a href="...">`标签提取页面链接
  - 自动补全相对路径为完整URL
  - 过滤外部链接和锚点链接
  - 去重处理，避免重复抓取
- **预览功能**：显示解析出的菜单树状结构，包含页面数量统计
- **URL规范化**：自动处理相对路径、绝对路径、锚点链接等

### 3. 内容抓取与处理（针对Astro+Starlight优化）
- **Starlight内容提取**：
  - 主要内容区域：`.sl-markdown-content`、`main`、`article`等
  - 过滤Starlight特有元素：`.header`、`.sidebar`、`.pagination`、`.edit-page`等
  - 保留代码块：`<pre><code>`、`.expressive-code`等
  - 保留Starlight组件：`.sl-badge`、`.aside`、`.steps`等
- **Markdown转换**：
  - 使用html2text进行HTML到Markdown转换
  - 保持Starlight特有格式（代码高亮、提示框、徽章等）
  - 处理特殊字符转义和中文内容
  - 保持标题层级结构
- **图片处理**：
  - 暂不下载图片，保留原始链接
- **链接处理**：
  - Starlight内部链接转换为相对路径
  - 处理`.astro`文件链接转换为`.md`
  - 外部链接保持原样
  - 正确处理锚点链接和页面内跳转

### 4. 文件组织与存储
- **目录结构**：
  - 严格按照网站菜单层级创建目录
  - 目录和文件名添加序号前缀（如`01-概述`、`02-快速开始`）
  - 支持中文文件名和特殊字符处理
- **文件命名规则**：
  - 自动生成合法的文件名（去除特殊字符）
  - 保持原有语义的同时确保文件系统兼容性
- **README生成**：
  - 自动生成完整的目录索引
  - 包含文档概述、目录结构、更新时间等信息
  - 支持多级目录的树状显示

### 5. 用户界面与交互
- **单窗口设计**：所有功能集成在一个主窗口中
- **配置面板**：
  - 目标URL输入框（支持基础URL验证）
  - 存储位置选择器
  - 线程数量选择（1-5个线程）
  - 抓取间隔时间设置（0.5-3秒）
- **预览区域**：
  - 树状菜单结构显示（支持层级展开/折叠）
  - 页面数量统计和预估时间
  - 与原网站菜单结构对比验证
- **操作控制**：
  - 预览按钮：显示将要抓取的页面列表
  - 开始按钮：启动抓取任务
  - 停止按钮：立即停止当前抓取任务
- **进度监控**：
  - 实时进度条和百分比
  - 当前处理页面显示
  - 成功/失败/跳过的页面统计
  - 详细日志输出

### 6. 基础功能
- **简单重试机制**：网络错误时重试1次
- **任务管理**：支持停止后重新开始抓取
- **编码处理**：基础UTF-8编码处理

## 技术实现要求

### 1. 核心依赖库（第一阶段实现）
- **GUI框架**：tkinter（快速实现，内置无需额外安装）
- **网络请求**：requests（简单可靠）
- **HTML解析**：BeautifulSoup4 + lxml（专门优化Starlight DOM解析）
- **Markdown转换**：html2text（轻量级，适合Starlight内容）
- **文件处理**：pathlib（现代化路径处理）
- **多线程**：threading + queue（基础并发支持）
- **扩展预留**：设计抽象基类，便于后续添加新框架支持

### 2. 错误处理与日志
- **异常捕获**：网络超时、解析错误、文件写入失败等
- **日志记录**：详细的操作日志，支持不同级别（DEBUG、INFO、WARNING、ERROR）
- **用户友好提示**：将技术错误转换为用户可理解的提示信息

### 3. 性能优化
- **内存管理**：基础内存使用控制
- **并发控制**：合理的线程池大小和请求频率控制

### 4. 扩展性设计
- **插件架构预留**：
  - 定义`DocumentFramework`抽象基类
  - `StarlightFramework`作为第一个具体实现
- **扩展点设计**：
  - 框架检测器接口
  - 菜单解析器接口  
  - 内容提取器接口

## 质量保证

### 1. 基础测试
- Astro+Starlight框架的基本功能测试
- 网络异常情况的基础处理

### 2. 用户体验
- 操作简单直观，新手友好

### 3. 兼容性
- 支持Windows 10/11
- 支持Python 3.8+
- 基础UTF-8编码处理

## 交付物
1. 完整的Python源代码
2. 基础使用说明