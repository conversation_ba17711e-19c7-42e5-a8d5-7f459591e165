#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径管理器

负责处理文件和目录的路径生成、命名规范化、字符处理等功能。
确保生成的路径在Windows文件系统中合法且语义清晰。

作者: Assistant
创建时间: 2025-01-27
"""

import re
import unicodedata
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set
from urllib.parse import urlparse, unquote

from .base import StorageConfig, StorageComponent, StorageError


class PathManager(StorageComponent):
    """路径管理器"""
    
    # Windows文件系统不支持的字符
    INVALID_CHARS = r'<>:"|?*'
    # 保留的文件名
    RESERVED_NAMES = {
        'CON', 'PRN', 'AUX', 'NUL',
        'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
        'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    }
    
    def __init__(self):
        super().__init__("PathManager")
        self._name_cache: Dict[str, str] = {}  # 名称缓存
        self._path_counter: Dict[str, int] = {}  # 路径计数器，处理重名
    
    def process(self, config: StorageConfig) -> bool:
        """处理配置，验证输出目录"""
        try:
            # 确保输出目录存在
            config.output_dir.mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            raise StorageError(f"无法创建输出目录: {e}", "path_creation", str(config.output_dir))
    
    def sanitize_filename(self, name: str, max_length: Optional[int] = None) -> str:
        """清理文件名，确保文件系统兼容性
        
        Args:
            name: 原始文件名
            max_length: 最大长度限制
            
        Returns:
            清理后的文件名
        """
        if not name or not name.strip():
            return "untitled"
        
        # 使用缓存
        cache_key = f"{name}_{max_length}"
        if cache_key in self._name_cache:
            return self._name_cache[cache_key]
        
        # 去除首尾空白
        cleaned = name.strip()
        
        # 替换无效字符
        for char in self.INVALID_CHARS:
            cleaned = cleaned.replace(char, '_')
        
        # 替换路径分隔符
        cleaned = cleaned.replace('/', '_').replace('\\', '_')
        
        # 处理连续的空格和下划线
        cleaned = re.sub(r'[\s_]+', '_', cleaned)
        
        # 去除首尾的点和空格（Windows不允许）
        cleaned = cleaned.strip('. ')
        
        # 处理保留名称
        name_upper = cleaned.upper()
        if name_upper in self.RESERVED_NAMES or name_upper.split('.')[0] in self.RESERVED_NAMES:
            cleaned = f"_{cleaned}"
        
        # 长度限制
        if max_length and len(cleaned) > max_length:
            # 保留扩展名
            if '.' in cleaned:
                name_part, ext = cleaned.rsplit('.', 1)
                max_name_length = max_length - len(ext) - 1
                if max_name_length > 0:
                    cleaned = name_part[:max_name_length] + '.' + ext
                else:
                    cleaned = cleaned[:max_length]
            else:
                cleaned = cleaned[:max_length]
        
        # 确保不为空
        if not cleaned:
            cleaned = "untitled"
        
        # 缓存结果
        self._name_cache[cache_key] = cleaned
        return cleaned
    
    def generate_filename(self, title: str, url: str, index: int = 0, 
                         config: Optional[StorageConfig] = None) -> str:
        """生成文件名
        
        Args:
            title: 页面标题
            url: 原始URL
            index: 序号
            config: 存储配置
            
        Returns:
            生成的文件名（不含扩展名）
        """
        # 优先使用标题，其次使用URL路径
        base_name = title.strip() if title and title.strip() else self._extract_name_from_url(url)
        
        # 清理文件名
        max_length = config.max_filename_length if config else 200
        # 为序号前缀预留空间
        if config and config.add_index_prefix:
            prefix_length = len(config.index_format.format(index)) + 1  # +1 for '-'
            max_length -= prefix_length
        
        cleaned_name = self.sanitize_filename(base_name, max_length)
        
        # 添加序号前缀
        if config and config.add_index_prefix:
            prefix = config.index_format.format(index)
            filename = f"{prefix}-{cleaned_name}"
        else:
            filename = cleaned_name
        
        return filename
    
    def generate_directory_name(self, name: str, index: int = 0, 
                               config: Optional[StorageConfig] = None) -> str:
        """生成目录名
        
        Args:
            name: 原始目录名
            index: 序号
            config: 存储配置
            
        Returns:
            生成的目录名
        """
        # 清理目录名
        max_length = config.max_filename_length if config else 200
        if config and config.add_index_prefix:
            prefix_length = len(config.index_format.format(index)) + 1
            max_length -= prefix_length
        
        cleaned_name = self.sanitize_filename(name, max_length)
        
        # 添加序号前缀
        if config and config.add_index_prefix:
            prefix = config.index_format.format(index)
            dir_name = f"{prefix}-{cleaned_name}"
        else:
            dir_name = cleaned_name
        
        return dir_name
    
    def resolve_path_conflict(self, path: Path) -> Path:
        """解决路径冲突
        
        Args:
            path: 原始路径
            
        Returns:
            解决冲突后的路径
        """
        if not path.exists():
            return path
        
        # 生成唯一路径
        base_path = path.parent
        name = path.stem
        suffix = path.suffix
        
        counter = 1
        while True:
            new_name = f"{name}_{counter}{suffix}"
            new_path = base_path / new_name
            if not new_path.exists():
                return new_path
            counter += 1
            
            # 防止无限循环
            if counter > 1000:
                raise StorageError(f"无法解决路径冲突: {path}", "path_conflict", str(path))
    
    def create_directory_structure(self, base_path: Path, structure: List[str], 
                                  config: Optional[StorageConfig] = None) -> List[Path]:
        """创建目录结构
        
        Args:
            base_path: 基础路径
            structure: 目录结构列表
            config: 存储配置
            
        Returns:
            创建的目录路径列表
        """
        created_dirs = []
        current_path = base_path
        
        for i, dir_name in enumerate(structure):
            if not dir_name.strip():
                continue
                
            # 生成目录名
            clean_dir_name = self.generate_directory_name(dir_name, i + 1, config)
            dir_path = current_path / clean_dir_name
            
            # 检查深度限制
            if config and config.max_depth > 0:
                relative_path = dir_path.relative_to(base_path)
                if len(relative_path.parts) > config.max_depth:
                    raise StorageError(
                        f"目录深度超过限制 ({config.max_depth}): {relative_path}",
                        "depth_exceeded",
                        str(dir_path)
                    )
            
            # 创建目录
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                created_dirs.append(dir_path)
                current_path = dir_path
            except Exception as e:
                raise StorageError(f"无法创建目录: {e}", "directory_creation", str(dir_path))
        
        return created_dirs
    
    def get_relative_path(self, file_path: Path, base_path: Path) -> Path:
        """获取相对路径
        
        Args:
            file_path: 文件路径
            base_path: 基础路径
            
        Returns:
            相对路径
        """
        try:
            return file_path.relative_to(base_path)
        except ValueError:
            # 如果无法计算相对路径，返回文件名
            return Path(file_path.name)
    
    def _extract_name_from_url(self, url: str) -> str:
        """从URL中提取名称
        
        Args:
            url: URL地址
            
        Returns:
            提取的名称
        """
        try:
            parsed = urlparse(url)
            path = unquote(parsed.path)
            
            # 移除扩展名
            if path.endswith('.html') or path.endswith('.htm'):
                path = path.rsplit('.', 1)[0]
            
            # 提取最后一个路径段
            if path and path != '/':
                name = path.strip('/').split('/')[-1]
                if name:
                    return name
            
            # 使用域名作为后备
            if parsed.netloc:
                return parsed.netloc.replace('.', '_')
            
            return "page"
        except Exception:
            return "page"
    
    def validate_path(self, path: Path) -> Tuple[bool, Optional[str]]:
        """验证路径是否合法
        
        Args:
            path: 要验证的路径
            
        Returns:
            (是否合法, 错误信息)
        """
        try:
            # 检查路径长度（Windows限制）
            if len(str(path)) > 260:
                return False, "路径长度超过Windows限制(260字符)"
            
            # 检查路径组件
            for part in path.parts:
                if not part:
                    continue
                    
                # 检查无效字符
                if any(char in part for char in self.INVALID_CHARS):
                    return False, f"路径包含无效字符: {part}"
                
                # 检查保留名称
                if part.upper() in self.RESERVED_NAMES:
                    return False, f"路径包含保留名称: {part}"
            
            return True, None
        except Exception as e:
            return False, f"路径验证失败: {e}"
    
    def clear_cache(self):
        """清空缓存"""
        self._name_cache.clear()
        self._path_counter.clear()