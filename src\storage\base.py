#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件存储模块基础类和数据结构

定义了文件存储的配置类、结果类和异常类，为文件组织与存储提供统一接口。

作者: Assistant
创建时间: 2025-01-27
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import time
from datetime import datetime


@dataclass
class StorageConfig:
    """存储配置类"""
    # 基础配置
    output_dir: Path  # 输出目录
    create_subdirs: bool = True  # 是否创建子目录
    max_depth: int = 5  # 最大目录深度
    
    # 文件命名配置
    add_index_prefix: bool = True  # 添加序号前缀
    index_format: str = "{:02d}"  # 序号格式
    sanitize_names: bool = True  # 清理文件名
    max_filename_length: int = 200  # 最大文件名长度
    
    # 文件内容配置
    encoding: str = "utf-8"  # 文件编码
    add_metadata: bool = True  # 添加元数据
    add_navigation: bool = True  # 添加导航链接
    generate_readme: bool = True  # 生成README文件
    
    # 安全配置
    atomic_write: bool = True  # 原子写入
    backup_existing: bool = False  # 备份已存在文件
    overwrite_existing: bool = True  # 覆盖已存在文件
    
    # 性能配置
    batch_size: int = 10  # 批处理大小
    use_threading: bool = False  # 使用多线程（文件IO通常不需要）
    
    def __post_init__(self):
        """配置验证和初始化"""
        # 确保输出目录是Path对象
        if isinstance(self.output_dir, str):
            self.output_dir = Path(self.output_dir)
        
        # 验证配置
        if self.max_depth < 1:
            raise ValueError("最大目录深度必须大于0")
        if self.max_filename_length < 10:
            raise ValueError("最大文件名长度不能小于10")


@dataclass
class FileInfo:
    """文件信息类"""
    # 基础信息
    title: str  # 文件标题
    url: str  # 原始URL
    content: str  # 文件内容
    
    # 路径信息
    relative_path: Optional[Path] = None  # 相对路径
    absolute_path: Optional[Path] = None  # 绝对路径
    filename: Optional[str] = None  # 文件名
    
    # 层级信息
    level: int = 0  # 层级深度
    index: int = 0  # 在同级中的序号
    parent_path: Optional[Path] = None  # 父目录路径
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_time: Optional[datetime] = None
    file_size: int = 0
    
    # 链接信息
    internal_links: List[str] = field(default_factory=list)
    external_links: List[str] = field(default_factory=list)
    images: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """初始化文件信息"""
        if self.created_time is None:
            self.created_time = datetime.now()
        if self.content:
            self.file_size = len(self.content.encode('utf-8'))


@dataclass
class DirectoryInfo:
    """目录信息类"""
    # 基础信息
    name: str  # 目录名
    path: Path  # 目录路径
    level: int = 0  # 层级深度
    index: int = 0  # 在同级中的序号
    
    # 内容信息
    files: List[FileInfo] = field(default_factory=list)
    subdirs: List['DirectoryInfo'] = field(default_factory=list)
    
    # 统计信息
    total_files: int = 0
    total_size: int = 0
    created_time: Optional[datetime] = None
    
    def __post_init__(self):
        """初始化目录信息"""
        if self.created_time is None:
            self.created_time = datetime.now()
    
    def update_statistics(self):
        """更新统计信息"""
        self.total_files = len(self.files)
        self.total_size = sum(f.file_size for f in self.files)
        
        # 递归统计子目录
        for subdir in self.subdirs:
            subdir.update_statistics()
            self.total_files += subdir.total_files
            self.total_size += subdir.total_size


@dataclass
class StorageResult:
    """存储结果类"""
    # 基础结果
    success: bool  # 是否成功
    message: str = ""  # 结果消息
    
    # 文件信息
    files_created: List[Path] = field(default_factory=list)
    dirs_created: List[Path] = field(default_factory=list)
    files_updated: List[Path] = field(default_factory=list)
    files_skipped: List[Path] = field(default_factory=list)
    
    # 统计信息
    total_files: int = 0
    total_dirs: int = 0
    total_size: int = 0
    processing_time: float = 0.0
    
    # 错误信息
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # 根目录信息
    root_dir: Optional[DirectoryInfo] = None
    
    def __post_init__(self):
        """初始化结果统计"""
        self.total_files = len(self.files_created) + len(self.files_updated)
        self.total_dirs = len(self.dirs_created)
    
    def add_error(self, error: str):
        """添加错误信息"""
        self.errors.append(error)
        if self.success and self.errors:
            self.success = False
    
    def add_warning(self, warning: str):
        """添加警告信息"""
        self.warnings.append(warning)
    
    def get_summary(self) -> str:
        """获取结果摘要"""
        if self.success:
            summary = f"存储成功: 创建{self.total_files}个文件，{self.total_dirs}个目录"
            if self.total_size > 0:
                size_mb = self.total_size / (1024 * 1024)
                summary += f"，总大小{size_mb:.2f}MB"
            if self.processing_time > 0:
                summary += f"，耗时{self.processing_time:.2f}秒"
        else:
            summary = f"存储失败: {self.message}"
            if self.errors:
                summary += f"，错误数量: {len(self.errors)}"
        
        if self.warnings:
            summary += f"，警告数量: {len(self.warnings)}"
        
        return summary


class StorageError(Exception):
    """存储异常类"""
    
    def __init__(self, message: str, error_type: str = "unknown", path: str = ""):
        super().__init__(message)
        self.message = message
        self.error_type = error_type
        self.path = path
    
    def __str__(self):
        result = f"StorageError({self.error_type}): {self.message}"
        if self.path:
            result += f" [路径: {self.path}]"
        return result


class StorageComponent(ABC):
    """存储组件抽象基类"""
    
    def __init__(self, name: str):
        self.name = name
    
    @abstractmethod
    def process(self, *args, **kwargs) -> Any:
        """处理方法，由子类实现"""
        pass
    
    def validate_config(self, config: StorageConfig) -> bool:
        """验证配置，子类可重写"""
        return True