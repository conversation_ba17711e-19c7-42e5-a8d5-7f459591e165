#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抓取管理器

负责协调多线程抓取任务，提供错误处理、进度跟踪和结果管理功能。

作者: Assistant
创建时间: 2025-07-30
"""

import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue, Empty
from typing import List, Dict, Callable, Optional, Iterator, Any
from dataclasses import dataclass, field
from pathlib import Path

from .base import ContentScraper, ScrapingConfig, ScrapingResult, ScrapingError
from .starlight_scraper import StarlightContentScraper


@dataclass
class ScrapingTask:
    """抓取任务"""
    url: str
    target_path: Optional[Path] = None
    metadata: Dict = field(default_factory=dict)
    priority: int = 0  # 优先级，数字越大优先级越高
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class ScrapingProgress:
    """抓取进度"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    current_task: str = ''
    start_time: float = field(default_factory=time.time)
    
    @property
    def progress_percentage(self) -> float:
        """进度百分比"""
        if self.total_tasks == 0:
            return 0.0
        return (self.completed_tasks + self.failed_tasks) / self.total_tasks * 100
    
    @property
    def elapsed_time(self) -> float:
        """已用时间（秒）"""
        return time.time() - self.start_time
    
    @property
    def estimated_remaining_time(self) -> float:
        """预计剩余时间（秒）"""
        if self.completed_tasks == 0:
            return 0.0
        
        avg_time_per_task = self.elapsed_time / (self.completed_tasks + self.failed_tasks)
        remaining_tasks = self.total_tasks - self.completed_tasks - self.failed_tasks
        return avg_time_per_task * remaining_tasks


class ScrapingManager:
    """抓取管理器"""
    
    def __init__(self, config: ScrapingConfig, max_workers: int = 4):
        """
        初始化抓取管理器
        
        Args:
            config: 抓取配置
            max_workers: 最大工作线程数
        """
        self.config = config
        self.max_workers = max_workers
        
        # 任务队列
        self.task_queue = Queue()
        self.result_queue = Queue()
        
        # 进度跟踪
        self.progress = ScrapingProgress()
        self.progress_lock = threading.Lock()
        
        # 回调函数
        self.progress_callback: Optional[Callable[[ScrapingProgress], None]] = None
        self.result_callback: Optional[Callable[[ScrapingResult], None]] = None
        self.error_callback: Optional[Callable[[str, Exception], None]] = None
        
        # 结果存储
        self.results: List[ScrapingResult] = []
        self.failed_tasks: List[ScrapingTask] = []
        
        # 控制标志
        self._stop_event = threading.Event()
        self._paused = False
        self._pause_lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_pages': 0,
            'successful_pages': 0,
            'failed_pages': 0,
            'total_content_length': 0,
            'total_processing_time': 0,
            'average_processing_time': 0
        }
    
    def add_task(self, task: ScrapingTask):
        """
        添加抓取任务
        
        Args:
            task: 抓取任务
        """
        self.task_queue.put(task)
        
        with self.progress_lock:
            self.progress.total_tasks += 1
    
    def add_tasks(self, tasks: List[ScrapingTask]):
        """
        批量添加抓取任务
        
        Args:
            tasks: 抓取任务列表
        """
        for task in tasks:
            self.task_queue.put(task)
        
        with self.progress_lock:
            self.progress.total_tasks += len(tasks)
    
    def add_urls(self, urls: List[str], target_dir: Optional[Path] = None):
        """
        从URL列表添加任务
        
        Args:
            urls: URL列表
            target_dir: 目标目录
        """
        tasks = []
        for url in urls:
            task = ScrapingTask(
                url=url,
                target_path=target_dir,
                max_retries=self.config.max_retries
            )
            tasks.append(task)
        
        self.add_tasks(tasks)
    
    def set_progress_callback(self, callback: Callable[[ScrapingProgress], None]):
        """
        设置进度回调函数
        
        Args:
            callback: 进度回调函数
        """
        self.progress_callback = callback
    
    def set_result_callback(self, callback: Callable[[ScrapingResult], None]):
        """
        设置结果回调函数
        
        Args:
            callback: 结果回调函数
        """
        self.result_callback = callback
    
    def set_error_callback(self, callback: Callable[[str, Exception], None]):
        """
        设置错误回调函数
        
        Args:
            callback: 错误回调函数
        """
        self.error_callback = callback
    
    def start_scraping(self) -> Iterator[ScrapingResult]:
        """
        开始抓取任务
        
        Yields:
            ScrapingResult: 抓取结果
        """
        if self.task_queue.empty():
            return
        
        # 重置状态
        self._stop_event.clear()
        self._paused = False
        self.progress.start_time = time.time()
        
        # 创建线程池
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_task = {}
            
            # 启动工作线程
            for _ in range(min(self.max_workers, self.task_queue.qsize())):
                if not self.task_queue.empty():
                    try:
                        task = self.task_queue.get_nowait()
                        future = executor.submit(self._process_task, task)
                        future_to_task[future] = task
                    except Empty:
                        break
            
            # 处理完成的任务
            for future in as_completed(future_to_task):
                if self._stop_event.is_set():
                    break
                
                task = future_to_task[future]
                
                try:
                    result = future.result()
                    self._handle_result(result)
                    yield result
                    
                    # 如果还有任务，提交新任务
                    if not self.task_queue.empty() and not self._stop_event.is_set():
                        try:
                            new_task = self.task_queue.get_nowait()
                            new_future = executor.submit(self._process_task, new_task)
                            future_to_task[new_future] = new_task
                        except Empty:
                            pass
                
                except Exception as e:
                    self._handle_error(task, e)
        
        # 更新最终统计信息
        self._update_final_stats()
    
    def _process_task(self, task: ScrapingTask) -> ScrapingResult:
        """
        处理单个抓取任务
        
        Args:
            task: 抓取任务
            
        Returns:
            ScrapingResult: 抓取结果
        """
        # 检查暂停状态
        self._wait_if_paused()
        
        # 检查停止标志
        if self._stop_event.is_set():
            return ScrapingResult(
                url=task.url,
                title='',
                content='',
                metadata={},
                success=False,
                error='任务被取消'
            )
        
        # 更新当前任务
        with self.progress_lock:
            self.progress.current_task = task.url
        
        # 触发进度回调
        if self.progress_callback:
            self.progress_callback(self.progress)
        
        # 创建抓取器
        scraper = StarlightContentScraper(self.config)
        
        try:
            # 执行抓取
            result = scraper.scrape(
                task.url,
                target_dir=task.target_path,
                metadata=task.metadata
            )
            
            return result
        
        except Exception as e:
            # 重试逻辑
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                # 重新加入队列
                self.task_queue.put(task)
                
                return ScrapingResult(
                    url=task.url,
                    title='',
                    content='',
                    metadata={},
                    success=False,
                    error=f'重试 {task.retry_count}/{task.max_retries}: {str(e)}'
                )
            else:
                return ScrapingResult(
                    url=task.url,
                    title='',
                    content='',
                    metadata={},
                    success=False,
                    error=f'最大重试次数已达到: {str(e)}'
                )
    
    def _handle_result(self, result: ScrapingResult):
        """
        处理抓取结果
        
        Args:
            result: 抓取结果
        """
        self.results.append(result)
        
        with self.progress_lock:
            if result.success:
                self.progress.completed_tasks += 1
            else:
                self.progress.failed_tasks += 1
        
        # 更新统计信息
        self._update_stats(result)
        
        # 触发结果回调
        if self.result_callback:
            self.result_callback(result)
        
        # 触发进度回调
        if self.progress_callback:
            self.progress_callback(self.progress)
    
    def _handle_error(self, task: ScrapingTask, error: Exception):
        """
        处理错误
        
        Args:
            task: 失败的任务
            error: 错误信息
        """
        self.failed_tasks.append(task)
        
        with self.progress_lock:
            self.progress.failed_tasks += 1
        
        # 触发错误回调
        if self.error_callback:
            self.error_callback(task.url, error)
        
        # 触发进度回调
        if self.progress_callback:
            self.progress_callback(self.progress)
    
    def _update_stats(self, result: ScrapingResult):
        """
        更新统计信息
        
        Args:
            result: 抓取结果
        """
        self.stats['total_pages'] += 1
        
        if result.success:
            self.stats['successful_pages'] += 1
            self.stats['total_content_length'] += len(result.content)
        else:
            self.stats['failed_pages'] += 1
        
        if result.processing_time:
            self.stats['total_processing_time'] += result.processing_time
            self.stats['average_processing_time'] = (
                self.stats['total_processing_time'] / self.stats['total_pages']
            )
    
    def _update_final_stats(self):
        """更新最终统计信息"""
        if self.stats['total_pages'] > 0:
            self.stats['success_rate'] = (
                self.stats['successful_pages'] / self.stats['total_pages'] * 100
            )
        else:
            self.stats['success_rate'] = 0.0
    
    def _wait_if_paused(self):
        """如果暂停则等待"""
        with self._pause_lock:
            while self._paused and not self._stop_event.is_set():
                time.sleep(0.1)
    
    def pause(self):
        """暂停抓取"""
        with self._pause_lock:
            self._paused = True
    
    def resume(self):
        """恢复抓取"""
        with self._pause_lock:
            self._paused = False
    
    def stop(self):
        """停止抓取"""
        self._stop_event.set()
        with self._pause_lock:
            self._paused = False
    
    def is_running(self) -> bool:
        """
        检查是否正在运行
        
        Returns:
            bool: 是否正在运行
        """
        return not self._stop_event.is_set()
    
    def is_paused(self) -> bool:
        """
        检查是否已暂停
        
        Returns:
            bool: 是否已暂停
        """
        with self._pause_lock:
            return self._paused
    
    def get_progress(self) -> ScrapingProgress:
        """
        获取当前进度
        
        Returns:
            ScrapingProgress: 进度信息
        """
        with self.progress_lock:
            return ScrapingProgress(
                total_tasks=self.progress.total_tasks,
                completed_tasks=self.progress.completed_tasks,
                failed_tasks=self.progress.failed_tasks,
                current_task=self.progress.current_task,
                start_time=self.progress.start_time
            )
    
    def get_results(self) -> List[ScrapingResult]:
        """
        获取所有抓取结果
        
        Returns:
            List[ScrapingResult]: 抓取结果列表
        """
        return self.results.copy()
    
    def get_successful_results(self) -> List[ScrapingResult]:
        """
        获取成功的抓取结果
        
        Returns:
            List[ScrapingResult]: 成功的抓取结果列表
        """
        return [result for result in self.results if result.success]
    
    def get_failed_results(self) -> List[ScrapingResult]:
        """
        获取失败的抓取结果
        
        Returns:
            List[ScrapingResult]: 失败的抓取结果列表
        """
        return [result for result in self.results if not result.success]
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.stats.copy()
    
    def clear_results(self):
        """清空结果"""
        self.results.clear()
        self.failed_tasks.clear()
        
        # 重置统计信息
        self.stats = {
            'total_pages': 0,
            'successful_pages': 0,
            'failed_pages': 0,
            'total_content_length': 0,
            'total_processing_time': 0,
            'average_processing_time': 0
        }
        
        # 重置进度
        with self.progress_lock:
            self.progress = ScrapingProgress()
    
    def export_results_summary(self) -> Dict[str, Any]:
        """
        导出结果摘要
        
        Returns:
            Dict[str, Any]: 结果摘要
        """
        successful_results = self.get_successful_results()
        failed_results = self.get_failed_results()
        
        return {
            'summary': {
                'total_tasks': len(self.results),
                'successful_tasks': len(successful_results),
                'failed_tasks': len(failed_results),
                'success_rate': len(successful_results) / len(self.results) * 100 if self.results else 0,
                'total_processing_time': sum(r.processing_time or 0 for r in self.results),
                'average_processing_time': self.stats.get('average_processing_time', 0)
            },
            'successful_urls': [r.url for r in successful_results],
            'failed_urls': [{'url': r.url, 'error': r.error} for r in failed_results],
            'content_stats': {
                'total_content_length': sum(len(r.content) for r in successful_results),
                'average_content_length': (
                    sum(len(r.content) for r in successful_results) / len(successful_results)
                    if successful_results else 0
                )
            }
        }