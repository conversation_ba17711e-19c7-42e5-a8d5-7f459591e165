#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容抓取与处理模块

本模块实现网站内容抓取和HTML到Markdown的转换功能，专门针对Astro+Starlight框架进行优化。

主要功能：
1. 网站内容抓取 - 支持多线程抓取，错误重试机制
2. HTML到Markdown转换 - 保持Starlight组件和结构
3. 链接处理 - 内部链接转换，外部链接保留
4. 图片处理 - 链接保留策略

核心组件：
- ContentScraper: 抓取器基类
- StarlightContentScraper: Starlight专用抓取器
- ScrapingManager: 抓取任务管理器
- MarkdownConverter: HTML到Markdown转换器
- LinkProcessor: 链接处理器
- ImageProcessor: 图片处理器

便捷函数：
- create_scraper(): 创建抓取器实例
- scrape_content(): 快速抓取内容
- create_manager(): 创建抓取管理器

作者: Assistant
创建时间: 2025-07-30
"""

from .base import ContentScraper, ScrapingConfig, ScrapingResult, ScrapingError
from .converter import MarkdownConverter
from .link_processor import LinkProcessor
from .image_processor import ImageProcessor
from .starlight_scraper import StarlightContentScraper
from .manager import ScrapingManager, ScrapingTask, ScrapingProgress

# 导出的公共API
__all__ = [
    # 基础类和配置
    'ScrapingConfig',
    'ScrapingResult', 
    'ScrapingError',
    'ContentScraper',
    
    # 核心组件
    'StarlightContentScraper',
    'ScrapingManager',
    'ScrapingTask',
    'ScrapingProgress',
    'MarkdownConverter',
    'LinkProcessor',
    'ImageProcessor',
    
    # 便捷函数
    'create_scraper',
    'scrape_content',
    'create_manager'
]


def create_scraper(config: ScrapingConfig = None) -> StarlightContentScraper:
    """
    创建Starlight内容抓取器实例
    
    Args:
        config: 抓取配置，如果为None则使用默认配置
        
    Returns:
        StarlightContentScraper: 抓取器实例
    """
    if config is None:
        config = ScrapingConfig()
    
    return StarlightContentScraper(config)


def scrape_content(url: str, config: ScrapingConfig = None) -> ScrapingResult:
    """
    快速抓取单个URL的内容
    
    Args:
        url: 目标URL
        config: 抓取配置，如果为None则使用默认配置
        
    Returns:
        ScrapingResult: 抓取结果
    """
    scraper = create_scraper(config)
    return scraper.scrape(url)


def create_manager(config: ScrapingConfig = None, max_workers: int = 4) -> ScrapingManager:
    """
    创建抓取管理器实例
    
    Args:
        config: 抓取配置，如果为None则使用默认配置
        max_workers: 最大工作线程数
        
    Returns:
        ScrapingManager: 抓取管理器实例
    """
    if config is None:
        config = ScrapingConfig()
    
    return ScrapingManager(config, max_workers)