#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件组织与存储模块

本模块负责将抓取的内容按照网站菜单结构组织并存储为本地Markdown文件，
确保文件结构清晰、命名规范。

主要功能：
- 目录结构管理：按菜单层级创建目录
- 文件命名规则：生成合法且语义清晰的文件名
- 内容组织：Markdown文件结构化存储
- README生成：自动生成目录索引

作者: Assistant
创建时间: 2025-01-27
"""

from .base import (
    StorageConfig,
    FileInfo,
    DirectoryInfo,
    StorageResult,
    StorageError
)

# 核心组件
from .path_manager import PathManager
from .file_organizer import FileOrganizer
from .content_writer import ContentWriter
from .readme_generator import ReadmeGenerator
from .storage_manager import StorageManager

__all__ = [
    # 基础类和数据结构
    'StorageConfig',
    'FileInfo',
    'DirectoryInfo', 
    'StorageResult',
    'StorageError',
    
    # 核心组件
    'PathManager',
    'FileOrganizer',
    'ContentWriter',
    'ReadmeGenerator',
    'StorageManager',
]

__version__ = '1.0.0'
__author__ = 'Assistant'