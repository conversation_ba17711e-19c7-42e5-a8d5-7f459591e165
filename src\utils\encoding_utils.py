#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编码处理工具模块

本模块提供编码检测、转换和字符处理功能，确保文本的正确处理和显示。
"""

import re
import os
import logging
from typing import Optional, Union, Dict, List
import chardet
from urllib.parse import unquote


class EncodingUtils:
    """编码处理工具类"""
    
    # 常见编码列表
    COMMON_ENCODINGS = [
        'utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'gb18030',
        'big5', 'latin1', 'ascii', 'cp1252'
    ]
    
    # 文件名非法字符映射
    ILLEGAL_CHARS = {
        '<': '＜',
        '>': '＞',
        ':': '：',
        '"': '＂',
        '/': '／',
        '\\': '＼',
        '|': '｜',
        '?': '？',
        '*': '＊'
    }
    
    # Windows保留文件名
    RESERVED_NAMES = {
        'CON', 'PRN', 'AUX', 'NUL',
        'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
        'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    }
    
    @classmethod
    def detect_encoding(cls, data: bytes, fallback: str = 'utf-8') -> str:
        """
        检测字节数据的编码
        
        Args:
            data: 字节数据
            fallback: 检测失败时的默认编码
            
        Returns:
            检测到的编码名称
        """
        if not data:
            return fallback
        
        # 检查BOM
        if data.startswith(b'\xef\xbb\xbf'):
            return 'utf-8-sig'
        elif data.startswith(b'\xff\xfe'):
            return 'utf-16-le'
        elif data.startswith(b'\xfe\xff'):
            return 'utf-16-be'
        
        # 使用chardet检测
        try:
            result = chardet.detect(data)
            if result and result['encoding']:
                confidence = result.get('confidence', 0)
                if confidence > 0.7:  # 置信度阈值
                    return result['encoding'].lower()
        except Exception:
            pass
        
        # 尝试常见编码
        for encoding in cls.COMMON_ENCODINGS:
            try:
                data.decode(encoding)
                return encoding
            except (UnicodeDecodeError, LookupError):
                continue
        
        return fallback
    
    @classmethod
    def safe_decode(cls, data: Union[bytes, str], encoding: Optional[str] = None, 
                   errors: str = 'replace') -> str:
        """
        安全解码字节数据为字符串
        
        Args:
            data: 要解码的数据
            encoding: 指定编码，None时自动检测
            errors: 错误处理方式
            
        Returns:
            解码后的字符串
        """
        if isinstance(data, str):
            return data
        
        if not isinstance(data, bytes):
            return str(data)
        
        if not data:
            return ''
        
        # 自动检测编码
        if encoding is None:
            encoding = cls.detect_encoding(data)
        
        try:
            return data.decode(encoding, errors=errors)
        except (UnicodeDecodeError, LookupError):
            # 尝试其他编码
            for fallback_encoding in cls.COMMON_ENCODINGS:
                if fallback_encoding != encoding:
                    try:
                        return data.decode(fallback_encoding, errors=errors)
                    except (UnicodeDecodeError, LookupError):
                        continue
            
            # 最后使用latin1（几乎不会失败）
            return data.decode('latin1', errors=errors)
    
    @classmethod
    def safe_encode(cls, text: str, encoding: str = 'utf-8', errors: str = 'replace') -> bytes:
        """
        安全编码字符串为字节数据
        
        Args:
            text: 要编码的字符串
            encoding: 目标编码
            errors: 错误处理方式
            
        Returns:
            编码后的字节数据
        """
        if isinstance(text, bytes):
            return text
        
        try:
            return text.encode(encoding, errors=errors)
        except (UnicodeEncodeError, LookupError):
            return text.encode('utf-8', errors=errors)
    
    @classmethod
    def sanitize_filename(cls, filename: str, max_length: int = 255, 
                         replacement: str = '_') -> str:
        """
        清理文件名，移除非法字符
        
        Args:
            filename: 原始文件名
            max_length: 最大长度
            replacement: 替换字符
            
        Returns:
            清理后的文件名
        """
        if not filename:
            return 'untitled'
        
        # 移除前后空白
        filename = filename.strip()
        
        # 替换非法字符
        for illegal_char, safe_char in cls.ILLEGAL_CHARS.items():
            filename = filename.replace(illegal_char, safe_char)
        
        # 移除控制字符
        filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', replacement, filename)
        
        # 移除连续的点和空格
        filename = re.sub(r'[.\s]+', lambda m: '.' if '.' in m.group() else ' ', filename)
        
        # 移除前后的点和空格
        filename = filename.strip('. ')
        
        # 检查保留名称
        name_without_ext = os.path.splitext(filename)[0].upper()
        if name_without_ext in cls.RESERVED_NAMES:
            filename = f"{replacement}{filename}"
        
        # 限制长度
        if len(filename) > max_length:
            name, ext = os.path.splitext(filename)
            max_name_length = max_length - len(ext)
            if max_name_length > 0:
                filename = name[:max_name_length] + ext
            else:
                filename = filename[:max_length]
        
        # 确保不为空
        if not filename:
            filename = 'untitled'
        
        return filename
    
    @classmethod
    def sanitize_path(cls, path: str, max_component_length: int = 255) -> str:
        """
        清理文件路径，处理每个路径组件
        
        Args:
            path: 原始路径
            max_component_length: 路径组件最大长度
            
        Returns:
            清理后的路径
        """
        if not path:
            return ''
        
        # 标准化路径分隔符
        path = path.replace('/', os.sep).replace('\\', os.sep)
        
        # 分割路径组件
        components = path.split(os.sep)
        
        # 清理每个组件
        cleaned_components = []
        for component in components:
            if component:  # 跳过空组件
                cleaned = cls.sanitize_filename(component, max_component_length)
                cleaned_components.append(cleaned)
        
        return os.sep.join(cleaned_components)
    
    @classmethod
    def normalize_text(cls, text: str) -> str:
        """
        标准化文本，处理特殊字符和空白
        
        Args:
            text: 原始文本
            
        Returns:
            标准化后的文本
        """
        if not text:
            return ''
        
        # 统一换行符
        text = text.replace('\r\n', '\n').replace('\r', '\n')
        
        # 移除零宽字符
        text = re.sub(r'[\u200b-\u200f\u2028-\u202f\u205f-\u206f\ufeff]', '', text)
        
        # 标准化空白字符
        text = re.sub(r'[\t\f\v]', ' ', text)
        
        # 移除行首行尾空白
        lines = text.split('\n')
        lines = [line.rstrip() for line in lines]
        
        # 移除多余的空行
        while lines and not lines[0]:
            lines.pop(0)
        while lines and not lines[-1]:
            lines.pop()
        
        return '\n'.join(lines)
    
    @classmethod
    def extract_encoding_from_html(cls, html_content: str) -> Optional[str]:
        """
        从HTML内容中提取编码信息
        
        Args:
            html_content: HTML内容
            
        Returns:
            提取到的编码名称
        """
        if not html_content:
            return None
        
        # 查找meta标签中的charset
        patterns = [
            r'<meta[^>]+charset=["\']?([^"\'>\s]+)',
            r'<meta[^>]+content=["\'][^"\'>]*charset=([^"\'>\s;]+)',
            r'<\?xml[^>]+encoding=["\']?([^"\'>\s]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, html_content, re.IGNORECASE)
            if match:
                encoding = match.group(1).lower().strip()
                # 标准化编码名称
                if encoding in ['gb2312', 'gbk']:
                    return 'gbk'
                elif encoding in ['utf8']:
                    return 'utf-8'
                elif encoding:
                    return encoding
        
        return None
    
    @classmethod
    def is_valid_encoding(cls, encoding: str) -> bool:
        """
        检查编码是否有效
        
        Args:
            encoding: 编码名称
            
        Returns:
            是否为有效编码
        """
        try:
            'test'.encode(encoding)
            return True
        except (LookupError, TypeError):
            return False


# 便捷函数
def detect_encoding(data: bytes, fallback: str = 'utf-8') -> str:
    """检测字节数据的编码"""
    return EncodingUtils.detect_encoding(data, fallback)


def safe_decode(data: Union[bytes, str], encoding: Optional[str] = None, 
               errors: str = 'replace') -> str:
    """安全解码字节数据为字符串"""
    return EncodingUtils.safe_decode(data, encoding, errors)


def sanitize_filename(filename: str, max_length: int = 255, replacement: str = '_') -> str:
    """清理文件名，移除非法字符"""
    return EncodingUtils.sanitize_filename(filename, max_length, replacement)


def sanitize_path(path: str, max_component_length: int = 255) -> str:
    """清理文件路径"""
    return EncodingUtils.sanitize_path(path, max_component_length)


def safe_encode(text: str, encoding: str = 'utf-8', errors: str = 'replace') -> bytes:
    """安全编码文本（便捷函数）"""
    return EncodingUtils.safe_encode(text, encoding, errors)


def clean_filename(filename: str, max_length: int = 255, replacement: str = '_') -> str:
    """清理文件名（便捷函数，sanitize_filename的别名）"""
    return sanitize_filename(filename, max_length, replacement)


def normalize_path(path: str, max_component_length: int = 255) -> str:
    """标准化路径（便捷函数，sanitize_path的别名）"""
    return sanitize_path(path, max_component_length)


def normalize_text(text: str) -> str:
    """标准化文本"""
    return EncodingUtils.normalize_text(text)


def extract_encoding_from_html(html_content: str) -> Optional[str]:
    """从HTML内容中提取编码信息"""
    return EncodingUtils.extract_encoding_from_html(html_content)