#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
存储模块测试

用于测试存储模块各个组件的基本功能。

作者: Assistant
创建时间: 2025-01-27
"""

from pathlib import Path
import tempfile
import shutil
from typing import List

from ...storage.base import StorageConfig, FileInfo, DirectoryInfo
from ...storage.path_manager import PathManager
from ...storage.file_organizer import FileOrganizer
from ...storage.content_writer import ContentWriter
from ...storage.readme_generator import ReadmeGenerator
from ...storage.storage_manager import StorageManager
from ...scraper.base import ScrapingResult


def create_test_scraping_results() -> List[ScrapingResult]:
    """创建测试用的抓取结果"""
    results = [
        ScrapingResult(
            url="https://example.com/docs/getting-started",
            title="快速开始",
            content="# 快速开始\n\n这是快速开始指南的内容。\n\n## 安装\n\n请按照以下步骤安装...",
            success=True,
            content_length=50,
            internal_links=["https://example.com/docs/installation"],
            external_links=["https://github.com/example/repo"]
        ),
        ScrapingResult(
            url="https://example.com/docs/installation",
            title="安装指南",
            content="# 安装指南\n\n详细的安装步骤说明。\n\n## 系统要求\n\n- Python 3.8+\n- Windows 10+",
            success=True,
            content_length=45,
            internal_links=[],
            external_links=["https://python.org"]
        ),
        ScrapingResult(
            url="https://example.com/docs/api/overview",
            title="API 概览",
            content="# API 概览\n\n本文档介绍了主要的API接口。\n\n## 认证\n\n所有API调用都需要认证...",
            success=True,
            content_length=60,
            internal_links=["https://example.com/docs/api/auth"],
            external_links=[]
        ),
        ScrapingResult(
            url="https://example.com/docs/api/auth",
            title="API 认证",
            content="# API 认证\n\n详细的认证流程说明。\n\n## Token 获取\n\n通过以下方式获取访问令牌...",
            success=True,
            content_length=55,
            internal_links=[],
            external_links=[]
        )
    ]
    return results


def test_path_manager():
    """测试路径管理器"""
    print("\n=== 测试路径管理器 ===")
    
    path_manager = PathManager()
    config = StorageConfig(
        output_dir=Path("test_output"),
        add_index_prefix=True,
        max_filename_length=50
    )
    
    # 测试文件名生成
    filename = path_manager.generate_filename("API 概览", "https://example.com/api", 1, config)
    print(f"生成的文件名: {filename}")
    
    # 测试目录名生成
    dirname = path_manager.generate_directory_name("API 文档", 2, config)
    print(f"生成的目录名: {dirname}")
    
    # 测试文件名清理
    clean_name = path_manager.sanitize_filename("文件名<>:\"/|?*测试")
    print(f"清理后的文件名: {clean_name}")
    
    print("路径管理器测试完成")


def test_file_organizer():
    """测试文件组织器"""
    print("\n=== 测试文件组织器 ===")
    
    organizer = FileOrganizer()
    config = StorageConfig(
        output_dir=Path("test_output"),
        add_index_prefix=True
    )
    
    # 创建测试数据
    results = create_test_scraping_results()
    
    # 组织文件结构
    root_dir = organizer.process(results, config, "https://example.com")
    
    print(f"根目录: {root_dir.name}")
    print(f"子目录数量: {len(root_dir.subdirs)}")
    print(f"文件数量: {len(root_dir.files)}")
    
    # 打印目录结构
    def print_structure(dir_info, indent=0):
        prefix = "  " * indent
        print(f"{prefix}📁 {dir_info.name}/")
        for file_info in dir_info.files:
            print(f"{prefix}  📄 {file_info.filename}")
        for subdir in dir_info.subdirs:
            print_structure(subdir, indent + 1)
    
    print("\n目录结构:")
    print_structure(root_dir)
    
    print("文件组织器测试完成")


def test_content_writer():
    """测试内容写入器"""
    print("\n=== 测试内容写入器 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        writer = ContentWriter()
        config = StorageConfig(
            output_dir=temp_path,
            add_metadata=True,
            add_navigation=True
        )
        
        # 创建测试文件信息
        file_info = FileInfo(
            title="测试文档",
            url="https://example.com/test",
            content="# 测试文档\n\n这是测试内容。",
            relative_path=Path("test.md"),
            absolute_path=temp_path / "test.md",
            filename="test.md",
            level=1,
            index=1,
            parent_path=temp_path
        )
        
        # 写入文件
        result = writer.process(file_info, config)
        
        print(f"写入结果: {result}")
        print(f"文件是否存在: {file_info.absolute_path.exists()}")
        
        if file_info.absolute_path.exists():
            content = file_info.absolute_path.read_text(encoding='utf-8')
            print(f"文件内容长度: {len(content)} 字符")
            print("文件内容预览:")
            print(content[:200] + "..." if len(content) > 200 else content)
    
    print("内容写入器测试完成")


def test_readme_generator():
    """测试README生成器"""
    print("\n=== 测试README生成器 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        generator = ReadmeGenerator()
        config = StorageConfig(
            output_dir=temp_path,
            generate_readme=True
        )
        
        # 创建测试目录结构
        root_dir = DirectoryInfo(
            name="docs",
            path=temp_path,
            level=0,
            index=0
        )
        
        # 添加测试文件
        file1 = FileInfo(
            title="快速开始",
            url="https://example.com/start",
            content="快速开始内容",
            relative_path=Path("start.md"),
            absolute_path=temp_path / "start.md",
            filename="01-快速开始.md",
            level=1,
            index=1,
            parent_path=temp_path
        )
        
        file2 = FileInfo(
            title="安装指南",
            url="https://example.com/install",
            content="安装指南内容",
            relative_path=Path("install.md"),
            absolute_path=temp_path / "install.md",
            filename="02-安装指南.md",
            level=1,
            index=2,
            parent_path=temp_path
        )
        
        root_dir.files = [file1, file2]
        
        # 生成README
        result = generator.process(root_dir, config)
        
        print(f"生成结果: {result}")
        
        readme_path = temp_path / "README.md"
        if readme_path.exists():
            content = readme_path.read_text(encoding='utf-8')
            print(f"README内容长度: {len(content)} 字符")
            print("README内容预览:")
            print(content[:300] + "..." if len(content) > 300 else content)
    
    print("README生成器测试完成")


def test_storage_manager():
    """测试存储管理器"""
    print("\n=== 测试存储管理器 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        manager = StorageManager()
        config = StorageConfig(
            output_dir=temp_path / "output",
            add_index_prefix=True,
            add_metadata=True,
            generate_readme=True,
            use_threading=False  # 测试时使用串行模式
        )
        
        # 创建测试数据
        results = create_test_scraping_results()
        
        # 执行存储
        storage_result = manager.store_content(
            results, config, "https://example.com"
        )
        
        print(f"存储成功: {storage_result.success}")
        print(f"总文件数: {storage_result.total_files}")
        print(f"成功文件数: {len(storage_result.files_created)}")
        print(f"失败文件数: {len(storage_result.errors)}")
        print(f"处理时间: {storage_result.processing_time:.3f}s")
        print(f"结果消息: {storage_result.message}")
        
        if storage_result.success:
            print(f"输出目录: {config.output_dir}")
            
            # 验证文件是否存在
            output_files = list(config.output_dir.rglob("*.md"))
            print(f"生成的文件数量: {len(output_files)}")
            
            for file_path in output_files:
                print(f"  📄 {file_path.relative_to(config.output_dir)}")
        else:
            print(f"存储失败: {storage_result.message}")
            if storage_result.errors:
                print(f"错误详情: {storage_result.errors}")
    
    print("存储管理器测试完成")


def run_all_tests():
    """运行所有测试"""
    print("开始运行存储模块测试...")
    
    try:
        test_path_manager()
        test_file_organizer()
        test_content_writer()
        test_readme_generator()
        test_storage_manager()
        
        print("\n🎉 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()