#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Starlight内容抓取器

专门针对Astro Starlight框架优化的内容抓取器，实现高效的文档内容提取和转换。

作者: Assistant
创建时间: 2025-07-30
"""

import requests
import time
from bs4 import BeautifulSoup, Tag
from urllib.parse import urljoin, urlparse
from typing import Optional, Dict, List, Set
from pathlib import Path

from .base import ContentScraper, ScrapingConfig, ScrapingResult, ScrapingError
from .converter import MarkdownConverter
from .link_processor import LinkProcessor
from .image_processor import ImageProcessor


class StarlightContentScraper(ContentScraper):
    """Starlight内容抓取器"""
    
    # Starlight特有的内容选择器
    STARLIGHT_SELECTORS = [
        '.sl-markdown-content',  # Starlight主要内容区域
        'main[data-pagefind-body]',  # 带有pagefind标记的主内容
        'main .content',  # 主内容区域
        'article',  # 文章内容
        'main',  # 主区域
        '.content'  # 通用内容区域
    ]
    
    # 需要移除的元素选择器
    REMOVE_SELECTORS = [
        '.sl-nav',  # 导航栏
        '.sidebar',  # 侧边栏
        '.header',  # 页头
        '.footer',  # 页脚
        '.breadcrumb',  # 面包屑
        '.pagination',  # 分页
        '.edit-page',  # 编辑页面链接
        '.last-updated',  # 最后更新时间
        '.contributors',  # 贡献者信息
        'nav',  # 导航元素
        '.toc',  # 目录（如果不需要）
        '.sl-banner',  # Starlight横幅
        '.sl-sidebar',  # Starlight侧边栏
        'script',  # 脚本
        'style',  # 样式
        'noscript'  # 无脚本内容
    ]
    
    # Starlight特有组件选择器
    STARLIGHT_COMPONENTS = {
        'aside': '.sl-aside, .starlight-aside',
        'badge': '.sl-badge, .starlight-badge',
        'card': '.sl-card, .starlight-card',
        'card-grid': '.sl-card-grid, .starlight-card-grid',
        'code': '.sl-code-block, .starlight-code',
        'steps': '.sl-steps, .starlight-steps',
        'tabs': '.sl-tabs, .starlight-tabs',
        'file-tree': '.sl-file-tree, .starlight-file-tree'
    }
    
    def __init__(self, config: ScrapingConfig):
        """
        初始化Starlight抓取器
        
        Args:
            config: 抓取配置
        """
        super().__init__("StarlightContentScraper")
        self.config = config
        
        # 初始化处理器
        self.markdown_converter = MarkdownConverter(config)
        self.link_processor = None  # 将在设置base_url时初始化
        self.image_processor = None  # 将在设置base_url时初始化
        
        # 会话配置
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 抓取统计
        self.stats = {
            'pages_scraped': 0,
            'pages_failed': 0,
            'total_content_length': 0,
            'processing_time': 0
        }
    
    def scrape(self, url: str, **kwargs) -> ScrapingResult:
        """
        抓取指定URL的内容
        
        Args:
            url: 目标URL
            **kwargs: 额外参数
            
        Returns:
            ScrapingResult: 抓取结果
        """
        start_time = time.time()
        
        try:
            # 初始化处理器（如果还未初始化）
            if not self.link_processor:
                base_url = self._extract_base_url(url)
                self.link_processor = LinkProcessor(base_url)
                self.image_processor = ImageProcessor(base_url)
            
            # 获取页面内容
            response = self._fetch_page(url)
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取页面元数据
            metadata = self._extract_metadata(soup, url)
            
            # 提取主要内容
            content_element = self._extract_content_element(soup)
            
            if not content_element:
                raise ScrapingError(f"无法找到有效的内容区域: {url}")
            
            # 预处理内容
            processed_content = self._preprocess_content(content_element)
            
            # 转换为Markdown
            markdown_content = self.markdown_converter.convert(str(processed_content))
            
            # 处理链接
            if self.config.process_links:
                target_dir = kwargs.get('target_dir')
                markdown_content, link_stats = self.link_processor.process_links(
                    markdown_content, target_dir
                )
                metadata['link_stats'] = link_stats
            
            # 处理图片
            if self.config.process_images:
                markdown_content, image_stats = self.image_processor.process_images(
                    markdown_content
                )
                metadata['image_stats'] = image_stats
            
            # 后处理
            markdown_content = self._postprocess_markdown(markdown_content)
            
            # 更新统计信息
            processing_time = time.time() - start_time
            self.stats['pages_scraped'] += 1
            self.stats['total_content_length'] += len(markdown_content)
            self.stats['processing_time'] += processing_time
            
            return ScrapingResult(
                url=url,
                title=metadata.get('title', ''),
                content=markdown_content,
                metadata=metadata,
                success=True,
                processing_time=processing_time
            )
        
        except Exception as e:
            self.stats['pages_failed'] += 1
            processing_time = time.time() - start_time
            
            return ScrapingResult(
                url=url,
                title='',
                content='',
                metadata={},
                success=False,
                error=str(e),
                processing_time=processing_time
            )
    
    def _fetch_page(self, url: str) -> requests.Response:
        """
        获取页面内容
        
        Args:
            url: 目标URL
            
        Returns:
            requests.Response: 响应对象
        """
        for attempt in range(self.config.max_retries + 1):
            try:
                response = self.session.get(
                    url,
                    timeout=self.config.timeout,
                    allow_redirects=True
                )
                response.raise_for_status()
                return response
            
            except requests.RequestException as e:
                if attempt == self.config.max_retries:
                    raise ScrapingError(f"获取页面失败 {url}: {str(e)}")
                
                # 等待后重试
                time.sleep(self.config.retry_delay * (attempt + 1))
    
    def _extract_base_url(self, url: str) -> str:
        """
        提取基础URL
        
        Args:
            url: 完整URL
            
        Returns:
            str: 基础URL
        """
        parsed = urlparse(url)
        return f"{parsed.scheme}://{parsed.netloc}"
    
    def _extract_metadata(self, soup: BeautifulSoup, url: str) -> Dict:
        """
        提取页面元数据
        
        Args:
            soup: BeautifulSoup对象
            url: 页面URL
            
        Returns:
            Dict: 元数据字典
        """
        metadata = {
            'url': url,
            'title': '',
            'description': '',
            'keywords': [],
            'author': '',
            'last_modified': '',
            'language': 'en'
        }
        
        # 提取标题
        title_tag = soup.find('title')
        if title_tag:
            metadata['title'] = title_tag.get_text().strip()
        
        # 提取H1作为备用标题
        if not metadata['title']:
            h1_tag = soup.find('h1')
            if h1_tag:
                metadata['title'] = h1_tag.get_text().strip()
        
        # 提取meta信息
        meta_tags = soup.find_all('meta')
        for meta in meta_tags:
            name = meta.get('name', '').lower()
            property_name = meta.get('property', '').lower()
            content = meta.get('content', '')
            
            if name == 'description' or property_name == 'og:description':
                metadata['description'] = content
            elif name == 'keywords':
                metadata['keywords'] = [k.strip() for k in content.split(',')]
            elif name == 'author':
                metadata['author'] = content
            elif name == 'last-modified':
                metadata['last_modified'] = content
        
        # 提取语言信息
        html_tag = soup.find('html')
        if html_tag and html_tag.get('lang'):
            metadata['language'] = html_tag.get('lang')
        
        return metadata
    
    def _extract_content_element(self, soup: BeautifulSoup) -> Optional[Tag]:
        """
        提取主要内容元素
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            Optional[Tag]: 内容元素
        """
        # 按优先级尝试选择器
        for selector in self.STARLIGHT_SELECTORS:
            element = soup.select_one(selector)
            if element and self._is_valid_content_element(element):
                return element
        
        return None
    
    def _is_valid_content_element(self, element: Tag) -> bool:
        """
        验证内容元素是否有效
        
        Args:
            element: 内容元素
            
        Returns:
            bool: 是否有效
        """
        if not element:
            return False
        
        # 检查文本长度
        text_content = element.get_text().strip()
        if len(text_content) < self.config.min_content_length:
            return False
        
        # 检查是否包含有意义的内容
        if not any(tag in element.name for tag in ['div', 'main', 'article', 'section']):
            # 如果不是容器元素，检查是否有足够的子元素
            if len(element.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'])) < 2:
                return False
        
        return True
    
    def _preprocess_content(self, content_element: Tag) -> Tag:
        """
        预处理内容元素
        
        Args:
            content_element: 内容元素
            
        Returns:
            Tag: 处理后的内容元素
        """
        # 创建副本以避免修改原始内容
        processed_element = BeautifulSoup(str(content_element), 'html.parser')
        
        # 移除不需要的元素
        for selector in self.REMOVE_SELECTORS:
            for element in processed_element.select(selector):
                element.decompose()
        
        # 处理Starlight特有组件
        self._process_starlight_components(processed_element)
        
        # 清理空元素
        self._clean_empty_elements(processed_element)
        
        return processed_element
    
    def _process_starlight_components(self, soup: BeautifulSoup):
        """
        处理Starlight特有组件
        
        Args:
            soup: BeautifulSoup对象
        """
        # 处理Aside组件
        for aside in soup.select(self.STARLIGHT_COMPONENTS['aside']):
            self._process_aside_component(aside)
        
        # 处理Badge组件
        for badge in soup.select(self.STARLIGHT_COMPONENTS['badge']):
            self._process_badge_component(badge)
        
        # 处理Card组件
        for card in soup.select(self.STARLIGHT_COMPONENTS['card']):
            self._process_card_component(card)
        
        # 处理Steps组件
        for steps in soup.select(self.STARLIGHT_COMPONENTS['steps']):
            self._process_steps_component(steps)
        
        # 处理Tabs组件
        for tabs in soup.select(self.STARLIGHT_COMPONENTS['tabs']):
            self._process_tabs_component(tabs)
    
    def _process_aside_component(self, aside: Tag):
        """处理Aside组件"""
        # 获取类型
        aside_type = 'note'  # 默认类型
        for class_name in aside.get('class', []):
            if 'caution' in class_name.lower():
                aside_type = 'caution'
            elif 'warning' in class_name.lower():
                aside_type = 'warning'
            elif 'danger' in class_name.lower():
                aside_type = 'danger'
            elif 'tip' in class_name.lower():
                aside_type = 'tip'
        
        # 添加Markdown标记
        aside.insert(0, f"\n> [!{aside_type.upper()}]\n> ")
        
        # 处理内容，添加引用前缀
        content = aside.get_text()
        lines = content.split('\n')
        processed_lines = [f"> {line}" if line.strip() else ">" for line in lines[1:]]  # 跳过第一行（已添加标记）
        
        aside.clear()
        aside.string = "\n".join([f"> [!{aside_type.upper()}]"] + processed_lines) + "\n"
    
    def _process_badge_component(self, badge: Tag):
        """处理Badge组件"""
        text = badge.get_text().strip()
        variant = 'default'
        
        # 检查变体
        for class_name in badge.get('class', []):
            if 'success' in class_name.lower():
                variant = 'success'
            elif 'warning' in class_name.lower():
                variant = 'warning'
            elif 'danger' in class_name.lower():
                variant = 'danger'
        
        # 转换为Markdown徽章格式
        badge.clear()
        badge.string = f"**{text}**" if variant == 'default' else f"**[{variant.upper()}] {text}**"
    
    def _process_card_component(self, card: Tag):
        """处理Card组件"""
        title_elem = card.select_one('.card-title, .sl-card-title')
        content_elem = card.select_one('.card-content, .sl-card-content')
        
        title = title_elem.get_text().strip() if title_elem else ''
        content = content_elem.get_text().strip() if content_elem else card.get_text().strip()
        
        # 转换为Markdown格式
        markdown_card = f"\n### {title}\n\n{content}\n" if title else f"\n{content}\n"
        
        card.clear()
        card.string = markdown_card
    
    def _process_steps_component(self, steps: Tag):
        """处理Steps组件"""
        step_items = steps.select('.step, .sl-step')
        
        if step_items:
            markdown_steps = "\n"
            for i, step in enumerate(step_items, 1):
                step_text = step.get_text().strip()
                markdown_steps += f"{i}. {step_text}\n"
            
            steps.clear()
            steps.string = markdown_steps + "\n"
    
    def _process_tabs_component(self, tabs: Tag):
        """处理Tabs组件"""
        tab_panels = tabs.select('.tab-panel, .sl-tab-panel')
        
        if tab_panels:
            markdown_tabs = "\n"
            for panel in tab_panels:
                label = panel.get('data-label', panel.get('aria-label', 'Tab'))
                content = panel.get_text().strip()
                markdown_tabs += f"#### {label}\n\n{content}\n\n"
            
            tabs.clear()
            tabs.string = markdown_tabs
    
    def _clean_empty_elements(self, soup: BeautifulSoup):
        """
        清理空元素
        
        Args:
            soup: BeautifulSoup对象
        """
        # 移除空的容器元素
        for tag in soup.find_all(['div', 'span', 'p']):
            if not tag.get_text().strip() and not tag.find_all(['img', 'br', 'hr']):
                tag.decompose()
    
    def _postprocess_markdown(self, markdown: str) -> str:
        """
        后处理Markdown内容
        
        Args:
            markdown: 原始Markdown内容
            
        Returns:
            str: 处理后的Markdown内容
        """
        # 清理多余的空行
        lines = markdown.split('\n')
        cleaned_lines = []
        prev_empty = False
        
        for line in lines:
            is_empty = not line.strip()
            
            if is_empty:
                if not prev_empty:
                    cleaned_lines.append('')
                prev_empty = True
            else:
                cleaned_lines.append(line)
                prev_empty = False
        
        # 移除开头和结尾的空行
        while cleaned_lines and not cleaned_lines[0].strip():
            cleaned_lines.pop(0)
        
        while cleaned_lines and not cleaned_lines[-1].strip():
            cleaned_lines.pop()
        
        return '\n'.join(cleaned_lines)
    
    def extract_content(self, html: str) -> str:
        """
        从HTML中提取内容
        
        Args:
            html: HTML内容
            
        Returns:
            str: 提取的文本内容
        """
        soup = BeautifulSoup(html, 'html.parser')
        content_element = self._extract_content_element(soup)
        
        if content_element:
            processed_content = self._preprocess_content(content_element)
            return processed_content.get_text()
        
        return ''
    
    def convert_to_markdown(self, html: str) -> str:
        """
        将HTML转换为Markdown
        
        Args:
            html: HTML内容
            
        Returns:
            str: Markdown内容
        """
        return self.markdown_converter.convert(html)
    
    def get_stats(self) -> Dict:
        """
        获取抓取统计信息
        
        Returns:
            Dict: 统计信息
        """
        return self.stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'pages_scraped': 0,
            'pages_failed': 0,
            'total_content_length': 0,
            'processing_time': 0
        }