#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件组织器

负责根据网站菜单结构组织文件和目录的层级关系，
将抓取结果转换为有序的文件系统结构。

作者: Assistant
创建时间: 2025-01-27
"""

from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlparse, unquote
import re

from .base import StorageConfig, StorageComponent, StorageError, FileInfo, DirectoryInfo
from .path_manager import PathManager
from ..scraper.base import ScrapingResult


class FileOrganizer(StorageComponent):
    """文件组织器"""
    
    def __init__(self):
        super().__init__("FileOrganizer")
        self.path_manager = PathManager()
        self._url_to_path_map: Dict[str, Path] = {}  # URL到路径的映射
        self._path_hierarchy: Dict[str, List[str]] = {}  # 路径层级关系
    
    def process(self, scraping_results: List[ScrapingResult], 
                config: StorageConfig, base_url: str) -> DirectoryInfo:
        """组织文件结构
        
        Args:
            scraping_results: 抓取结果列表
            config: 存储配置
            base_url: 基础URL
            
        Returns:
            根目录信息
        """
        # 分析URL结构
        url_structure = self._analyze_url_structure(scraping_results, base_url)
        
        # 构建目录树
        root_dir = self._build_directory_tree(url_structure, config)
        
        # 组织文件
        self._organize_files(scraping_results, root_dir, config, base_url)
        
        # 更新统计信息
        root_dir.update_statistics()
        
        return root_dir
    
    def _analyze_url_structure(self, scraping_results: List[ScrapingResult], 
                              base_url: str) -> Dict[str, Any]:
        """分析URL结构
        
        Args:
            scraping_results: 抓取结果列表
            base_url: 基础URL
            
        Returns:
            URL结构信息
        """
        structure = {
            'base_url': base_url,
            'url_paths': {},  # URL -> 路径段列表
            'path_hierarchy': {},  # 路径层级关系
            'url_titles': {}  # URL -> 标题映射
        }
        
        base_parsed = urlparse(base_url)
        base_path = base_parsed.path.rstrip('/')
        
        for result in scraping_results:
            if not result.success or not result.url:
                continue
            
            # 解析URL路径
            parsed = urlparse(result.url)
            if parsed.netloc != base_parsed.netloc:
                continue  # 跳过外部链接
            
            # 获取相对路径
            full_path = unquote(parsed.path)
            if full_path.startswith(base_path):
                relative_path = full_path[len(base_path):].strip('/')
            else:
                relative_path = full_path.strip('/')
            
            # 分割路径段
            if relative_path:
                path_segments = [seg for seg in relative_path.split('/') if seg]
            else:
                path_segments = []
            
            # 存储信息
            structure['url_paths'][result.url] = path_segments
            structure['url_titles'][result.url] = result.title or ""
        
        return structure
    
    def _build_directory_tree(self, url_structure: Dict[str, Any], 
                             config: StorageConfig) -> DirectoryInfo:
        """构建目录树
        
        Args:
            url_structure: URL结构信息
            config: 存储配置
            
        Returns:
            根目录信息
        """
        # 创建根目录
        root_dir = DirectoryInfo(
            name="docs",
            path=config.output_dir,
            level=0,
            index=0
        )
        
        # 收集所有路径段
        all_paths = set()
        for path_segments in url_structure['url_paths'].values():
            # 添加所有前缀路径
            for i in range(len(path_segments)):
                path_prefix = tuple(path_segments[:i+1])
                all_paths.add(path_prefix)
        
        # 按层级排序
        sorted_paths = sorted(all_paths, key=lambda x: (len(x), x))
        
        # 构建目录结构
        path_to_dir: Dict[tuple, DirectoryInfo] = {(): root_dir}
        
        for path_tuple in sorted_paths:
            if not path_tuple:
                continue
            
            parent_path = path_tuple[:-1]
            current_segment = path_tuple[-1]
            
            # 获取父目录
            parent_dir = path_to_dir.get(parent_path, root_dir)
            
            # 检查是否已存在
            existing_dir = None
            for subdir in parent_dir.subdirs:
                if subdir.name == current_segment:
                    existing_dir = subdir
                    break
            
            if existing_dir:
                path_to_dir[path_tuple] = existing_dir
            else:
                # 创建新目录
                dir_index = len(parent_dir.subdirs) + 1
                clean_name = self.path_manager.generate_directory_name(
                    current_segment, dir_index, config
                )
                
                new_dir = DirectoryInfo(
                    name=clean_name,
                    path=parent_dir.path / clean_name,
                    level=len(path_tuple),
                    index=dir_index
                )
                
                parent_dir.subdirs.append(new_dir)
                path_to_dir[path_tuple] = new_dir
        
        return root_dir
    
    def _organize_files(self, scraping_results: List[ScrapingResult], 
                       root_dir: DirectoryInfo, config: StorageConfig, 
                       base_url: str):
        """组织文件到目录结构中
        
        Args:
            scraping_results: 抓取结果列表
            root_dir: 根目录信息
            config: 存储配置
            base_url: 基础URL
        """
        # 构建目录映射
        dir_map = self._build_directory_map(root_dir)
        
        # 按URL路径分组
        url_groups = self._group_by_url_path(scraping_results, base_url)
        
        # 为每个组分配文件
        for path_key, results in url_groups.items():
            target_dir = self._find_target_directory(path_key, dir_map, root_dir)
            
            # 按标题排序
            sorted_results = sorted(results, key=lambda r: r.title or r.url)
            
            # 创建文件信息
            for i, result in enumerate(sorted_results):
                if not result.success:
                    continue
                
                file_info = self._create_file_info(result, target_dir, i + 1, config)
                target_dir.files.append(file_info)
    
    def _build_directory_map(self, root_dir: DirectoryInfo) -> Dict[tuple, DirectoryInfo]:
        """构建目录映射
        
        Args:
            root_dir: 根目录信息
            
        Returns:
            路径元组到目录信息的映射
        """
        dir_map = {(): root_dir}
        
        def traverse(dir_info: DirectoryInfo, path_tuple: tuple):
            for subdir in dir_info.subdirs:
                subdir_path = path_tuple + (subdir.name,)
                dir_map[subdir_path] = subdir
                traverse(subdir, subdir_path)
        
        traverse(root_dir, ())
        return dir_map
    
    def _group_by_url_path(self, scraping_results: List[ScrapingResult], 
                          base_url: str) -> Dict[tuple, List[ScrapingResult]]:
        """按URL路径分组
        
        Args:
            scraping_results: 抓取结果列表
            base_url: 基础URL
            
        Returns:
            路径元组到结果列表的映射
        """
        groups = {}
        base_parsed = urlparse(base_url)
        base_path = base_parsed.path.rstrip('/')
        
        for result in scraping_results:
            if not result.success or not result.url:
                continue
            
            # 解析URL路径
            parsed = urlparse(result.url)
            if parsed.netloc != base_parsed.netloc:
                continue
            
            # 获取路径段
            full_path = unquote(parsed.path)
            if full_path.startswith(base_path):
                relative_path = full_path[len(base_path):].strip('/')
            else:
                relative_path = full_path.strip('/')
            
            if relative_path:
                path_segments = tuple(seg for seg in relative_path.split('/') if seg)
                # 移除文件名部分，只保留目录路径
                if path_segments and not self._is_directory_path(result.url):
                    path_segments = path_segments[:-1]
            else:
                path_segments = ()
            
            if path_segments not in groups:
                groups[path_segments] = []
            groups[path_segments].append(result)
        
        return groups
    
    def _find_target_directory(self, path_key: tuple, dir_map: Dict[tuple, DirectoryInfo], 
                              root_dir: DirectoryInfo) -> DirectoryInfo:
        """查找目标目录
        
        Args:
            path_key: 路径键
            dir_map: 目录映射
            root_dir: 根目录
            
        Returns:
            目标目录信息
        """
        # 尝试精确匹配
        if path_key in dir_map:
            return dir_map[path_key]
        
        # 尝试部分匹配
        for i in range(len(path_key), 0, -1):
            partial_key = path_key[:i]
            if partial_key in dir_map:
                return dir_map[partial_key]
        
        # 返回根目录
        return root_dir
    
    def _create_file_info(self, result: ScrapingResult, target_dir: DirectoryInfo, 
                         index: int, config: StorageConfig) -> FileInfo:
        """创建文件信息
        
        Args:
            result: 抓取结果
            target_dir: 目标目录
            index: 文件序号
            config: 存储配置
            
        Returns:
            文件信息
        """
        # 生成文件名
        filename = self.path_manager.generate_filename(
            result.title or "", result.url, index, config
        ) + ".md"
        
        # 创建文件路径
        absolute_path = target_dir.path / filename
        relative_path = self.path_manager.get_relative_path(absolute_path, config.output_dir)
        
        # 创建文件信息
        file_info = FileInfo(
            title=result.title or "",
            url=result.url,
            content=result.content,
            relative_path=relative_path,
            absolute_path=absolute_path,
            filename=filename,
            level=target_dir.level + 1,
            index=index,
            parent_path=target_dir.path,
            internal_links=result.internal_links,
            external_links=result.external_links,
            images=result.images
        )
        
        # 添加元数据
        file_info.metadata.update({
            'scraping_time': getattr(result, 'scraping_time', 0),
            'processing_time': getattr(result, 'processing_time', 0),
            'content_length': result.content_length
        })
        
        return file_info
    
    def _is_directory_path(self, url: str) -> bool:
        """判断URL是否指向目录
        
        Args:
            url: URL地址
            
        Returns:
            是否是目录路径
        """
        parsed = urlparse(url)
        path = parsed.path
        
        # 以/结尾通常是目录
        if path.endswith('/'):
            return True
        
        # 没有扩展名可能是目录
        if '.' not in Path(path).name:
            return True
        
        return False
    
    def organize_by_menu_structure(self, scraping_results: List[ScrapingResult],
                                  menu_structure: List[Dict[str, Any]], 
                                  config: StorageConfig) -> DirectoryInfo:
        """根据菜单结构组织文件
        
        Args:
            scraping_results: 抓取结果列表
            menu_structure: 菜单结构数据
            config: 存储配置
            
        Returns:
            根目录信息
        """
        # 创建根目录
        root_dir = DirectoryInfo(
            name="docs",
            path=config.output_dir,
            level=0,
            index=0
        )
        
        # 创建URL到结果的映射
        url_to_result = {result.url: result for result in scraping_results if result.success}
        
        # 递归处理菜单结构
        self._process_menu_items(menu_structure, root_dir, url_to_result, config, 1)
        
        # 处理未匹配的文件
        self._handle_unmatched_files(scraping_results, root_dir, config)
        
        # 更新统计信息
        root_dir.update_statistics()
        
        return root_dir
    
    def _process_menu_items(self, menu_items: List[Dict[str, Any]], 
                           parent_dir: DirectoryInfo, 
                           url_to_result: Dict[str, ScrapingResult],
                           config: StorageConfig, start_index: int):
        """处理菜单项
        
        Args:
            menu_items: 菜单项列表
            parent_dir: 父目录
            url_to_result: URL到结果的映射
            config: 存储配置
            start_index: 起始序号
        """
        for i, item in enumerate(menu_items):
            index = start_index + i
            
            # 检查是否有子项
            has_children = 'children' in item and item['children']
            item_url = item.get('url', '')
            item_title = item.get('title', item.get('text', ''))
            
            if has_children:
                # 创建子目录
                dir_name = self.path_manager.generate_directory_name(
                    item_title, index, config
                )
                
                subdir = DirectoryInfo(
                    name=dir_name,
                    path=parent_dir.path / dir_name,
                    level=parent_dir.level + 1,
                    index=index
                )
                
                parent_dir.subdirs.append(subdir)
                
                # 如果当前项也有URL，创建索引文件
                if item_url and item_url in url_to_result:
                    result = url_to_result[item_url]
                    file_info = self._create_file_info(result, subdir, 0, config)
                    file_info.filename = "README.md"
                    file_info.absolute_path = subdir.path / "README.md"
                    subdir.files.append(file_info)
                    del url_to_result[item_url]  # 标记为已处理
                
                # 递归处理子项
                self._process_menu_items(item['children'], subdir, url_to_result, config, 1)
            
            elif item_url and item_url in url_to_result:
                # 创建文件
                result = url_to_result[item_url]
                file_info = self._create_file_info(result, parent_dir, index, config)
                parent_dir.files.append(file_info)
                del url_to_result[item_url]  # 标记为已处理
    
    def _handle_unmatched_files(self, scraping_results: List[ScrapingResult],
                               root_dir: DirectoryInfo, config: StorageConfig):
        """处理未匹配的文件
        
        Args:
            scraping_results: 抓取结果列表
            root_dir: 根目录
            config: 存储配置
        """
        unmatched_results = [r for r in scraping_results if r.success and r.url]
        
        if not unmatched_results:
            return
        
        # 创建其他文件目录
        other_dir = DirectoryInfo(
            name="其他文件",
            path=root_dir.path / "others",
            level=1,
            index=len(root_dir.subdirs) + 1
        )
        
        root_dir.subdirs.append(other_dir)
        
        # 添加未匹配的文件
        for i, result in enumerate(unmatched_results):
            file_info = self._create_file_info(result, other_dir, i + 1, config)
            other_dir.files.append(file_info)