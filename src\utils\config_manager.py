#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块

本模块提供统一的配置管理功能，支持运行时配置验证、默认值处理和实时生效机制。
"""

import os
import json
import yaml
from typing import Any, Dict, Optional, Union, Callable, List, Type
from pathlib import Path
from dataclasses import dataclass, field, asdict
from enum import Enum
import threading
from copy import deepcopy


class ConfigFormat(Enum):
    """配置文件格式"""
    JSON = 'json'
    YAML = 'yaml'
    YML = 'yml'


@dataclass
class ValidationRule:
    """配置验证规则"""
    validator: Callable[[Any], bool]
    error_message: str
    required: bool = False


@dataclass
class ConfigField:
    """配置字段定义"""
    name: str
    default_value: Any = None
    description: str = ""
    validation_rules: List[ValidationRule] = field(default_factory=list)
    type_hint: Optional[Type] = None
    sensitive: bool = False  # 敏感信息标记


class ConfigSchema:
    """配置模式定义"""
    
    def __init__(self):
        self.fields: Dict[str, ConfigField] = {}
        self.groups: Dict[str, List[str]] = {}  # 配置分组
    
    def add_field(self, field: ConfigField) -> 'ConfigSchema':
        """添加配置字段"""
        self.fields[field.name] = field
        return self
    
    def add_group(self, group_name: str, field_names: List[str]) -> 'ConfigSchema':
        """添加配置分组"""
        self.groups[group_name] = field_names
        return self
    
    def get_defaults(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {name: field.default_value for name, field in self.fields.items()}
    
    def validate(self, config: Dict[str, Any]) -> List[str]:
        """验证配置"""
        errors = []
        
        for name, field in self.fields.items():
            value = config.get(name)
            
            # 检查必填字段
            if any(rule.required for rule in field.validation_rules):
                if value is None:
                    errors.append(f"必填字段 '{name}' 缺失")
                    continue
            
            # 如果值为None且有默认值，跳过验证
            if value is None and field.default_value is not None:
                continue
            
            # 类型检查
            if field.type_hint and value is not None:
                if not isinstance(value, field.type_hint):
                    errors.append(f"字段 '{name}' 类型错误，期望 {field.type_hint.__name__}")
                    continue
            
            # 自定义验证规则
            for rule in field.validation_rules:
                if value is not None and not rule.validator(value):
                    errors.append(f"字段 '{name}': {rule.error_message}")
        
        return errors


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, schema: Optional[ConfigSchema] = None):
        self.schema = schema or ConfigSchema()
        self.config: Dict[str, Any] = {}
        self.config_file: Optional[Path] = None
        self.config_format: ConfigFormat = ConfigFormat.JSON
        self.auto_save: bool = False
        self.change_callbacks: List[Callable[[str, Any, Any], None]] = []
        self._lock = threading.RLock()
        
        # 加载默认配置
        self.reset_to_defaults()
    
    def set_schema(self, schema: ConfigSchema):
        """设置配置模式"""
        with self._lock:
            self.schema = schema
            # 重新应用默认值
            defaults = schema.get_defaults()
            for key, value in defaults.items():
                if key not in self.config:
                    self.config[key] = value
    
    def load_from_file(self, file_path: Union[str, Path], 
                      format_type: Optional[ConfigFormat] = None) -> bool:
        """从文件加载配置"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return False
            
            # 自动检测格式
            if format_type is None:
                suffix = file_path.suffix.lower()
                if suffix == '.json':
                    format_type = ConfigFormat.JSON
                elif suffix in ['.yaml', '.yml']:
                    format_type = ConfigFormat.YAML
                else:
                    format_type = ConfigFormat.JSON
            
            with open(file_path, 'r', encoding='utf-8') as f:
                if format_type == ConfigFormat.JSON:
                    data = json.load(f)
                else:  # YAML
                    data = yaml.safe_load(f)
            
            with self._lock:
                # 验证配置
                errors = self.schema.validate(data)
                if errors:
                    raise ValueError(f"配置验证失败: {'; '.join(errors)}")
                
                # 合并配置（保留默认值）
                old_config = deepcopy(self.config)
                self.config.update(data)
                
                self.config_file = file_path
                self.config_format = format_type
                
                # 触发变更回调
                self._notify_changes(old_config, self.config)
            
            return True
            
        except Exception as e:
            raise RuntimeError(f"加载配置文件失败: {e}")
    
    def save_to_file(self, file_path: Optional[Union[str, Path]] = None,
                    format_type: Optional[ConfigFormat] = None) -> bool:
        """保存配置到文件"""
        try:
            if file_path is None:
                file_path = self.config_file
            if file_path is None:
                raise ValueError("未指定配置文件路径")
            
            file_path = Path(file_path)
            
            if format_type is None:
                format_type = self.config_format
            
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with self._lock:
                # 过滤敏感信息（如果需要）
                save_config = self._filter_sensitive_data(self.config)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    if format_type == ConfigFormat.JSON:
                        json.dump(save_config, f, indent=2, ensure_ascii=False)
                    else:  # YAML
                        yaml.dump(save_config, f, default_flow_style=False, 
                                allow_unicode=True, indent=2)
                
                self.config_file = file_path
                self.config_format = format_type
            
            return True
            
        except Exception as e:
            raise RuntimeError(f"保存配置文件失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        with self._lock:
            return self.config.get(key, default)
    
    def set(self, key: str, value: Any, validate: bool = True) -> bool:
        """设置配置值"""
        with self._lock:
            # 验证单个字段
            if validate and key in self.schema.fields:
                field = self.schema.fields[key]
                
                # 类型检查
                if field.type_hint and value is not None:
                    if not isinstance(value, field.type_hint):
                        raise ValueError(f"字段 '{key}' 类型错误，期望 {field.type_hint.__name__}")
                
                # 自定义验证
                for rule in field.validation_rules:
                    if value is not None and not rule.validator(value):
                        raise ValueError(f"字段 '{key}': {rule.error_message}")
            
            old_value = self.config.get(key)
            self.config[key] = value
            
            # 触发变更回调
            for callback in self.change_callbacks:
                try:
                    callback(key, old_value, value)
                except Exception:
                    pass  # 忽略回调错误
            
            # 自动保存
            if self.auto_save and self.config_file:
                try:
                    self.save_to_file()
                except Exception:
                    pass  # 忽略保存错误
            
            return True
    
    def update(self, updates: Dict[str, Any], validate: bool = True) -> bool:
        """批量更新配置"""
        with self._lock:
            # 验证所有更新
            if validate:
                temp_config = deepcopy(self.config)
                temp_config.update(updates)
                errors = self.schema.validate(temp_config)
                if errors:
                    raise ValueError(f"配置验证失败: {'; '.join(errors)}")
            
            old_config = deepcopy(self.config)
            self.config.update(updates)
            
            # 触发变更回调
            self._notify_changes(old_config, self.config)
            
            # 自动保存
            if self.auto_save and self.config_file:
                try:
                    self.save_to_file()
                except Exception:
                    pass
            
            return True
    
    def delete(self, key: str) -> bool:
        """删除配置项"""
        with self._lock:
            if key in self.config:
                old_value = self.config.pop(key)
                
                # 触发变更回调
                for callback in self.change_callbacks:
                    try:
                        callback(key, old_value, None)
                    except Exception:
                        pass
                
                return True
            return False
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        with self._lock:
            old_config = deepcopy(self.config)
            self.config = self.schema.get_defaults()
            self._notify_changes(old_config, self.config)
    
    def get_group(self, group_name: str) -> Dict[str, Any]:
        """获取配置分组"""
        if group_name not in self.schema.groups:
            return {}
        
        with self._lock:
            return {key: self.config.get(key) 
                   for key in self.schema.groups[group_name] 
                   if key in self.config}
    
    def set_group(self, group_name: str, values: Dict[str, Any], validate: bool = True):
        """设置配置分组"""
        if group_name not in self.schema.groups:
            raise ValueError(f"未知的配置分组: {group_name}")
        
        # 只更新分组内的字段
        group_fields = set(self.schema.groups[group_name])
        filtered_values = {k: v for k, v in values.items() if k in group_fields}
        
        self.update(filtered_values, validate)
    
    def add_change_callback(self, callback: Callable[[str, Any, Any], None]):
        """添加配置变更回调"""
        if callback not in self.change_callbacks:
            self.change_callbacks.append(callback)
    
    def remove_change_callback(self, callback: Callable[[str, Any, Any], None]):
        """移除配置变更回调"""
        if callback in self.change_callbacks:
            self.change_callbacks.remove(callback)
    
    def enable_auto_save(self, enabled: bool = True):
        """启用/禁用自动保存"""
        self.auto_save = enabled
    
    def validate_current_config(self) -> List[str]:
        """验证当前配置"""
        with self._lock:
            return self.schema.validate(self.config)
    
    def export_config(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """导出配置"""
        with self._lock:
            if include_sensitive:
                return deepcopy(self.config)
            else:
                return self._filter_sensitive_data(self.config)
    
    def _filter_sensitive_data(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """过滤敏感数据"""
        filtered = {}
        for key, value in config.items():
            field = self.schema.fields.get(key)
            if field and field.sensitive:
                filtered[key] = "***"  # 敏感信息掩码
            else:
                filtered[key] = value
        return filtered
    
    def _notify_changes(self, old_config: Dict[str, Any], new_config: Dict[str, Any]):
        """通知配置变更"""
        # 找出变更的字段
        all_keys = set(old_config.keys()) | set(new_config.keys())
        
        for key in all_keys:
            old_value = old_config.get(key)
            new_value = new_config.get(key)
            
            if old_value != new_value:
                for callback in self.change_callbacks:
                    try:
                        callback(key, old_value, new_value)
                    except Exception:
                        pass  # 忽略回调错误


# 创建默认配置模式
def create_default_schema() -> ConfigSchema:
    """创建默认配置模式"""
    schema = ConfigSchema()
    
    # 基础配置
    schema.add_field(ConfigField(
        name='max_workers',
        default_value=4,
        description='最大工作线程数',
        type_hint=int,
        validation_rules=[
            ValidationRule(lambda x: 1 <= x <= 20, '工作线程数必须在1-20之间')
        ]
    ))
    
    schema.add_field(ConfigField(
        name='timeout',
        default_value=30,
        description='请求超时时间（秒）',
        type_hint=int,
        validation_rules=[
            ValidationRule(lambda x: x > 0, '超时时间必须大于0')
        ]
    ))
    
    schema.add_field(ConfigField(
        name='retry_times',
        default_value=3,
        description='重试次数',
        type_hint=int,
        validation_rules=[
            ValidationRule(lambda x: 0 <= x <= 10, '重试次数必须在0-10之间')
        ]
    ))
    
    schema.add_field(ConfigField(
        name='output_dir',
        default_value='./output',
        description='输出目录',
        type_hint=str,
        validation_rules=[
            ValidationRule(lambda x: len(x.strip()) > 0, '输出目录不能为空')
        ]
    ))
    
    schema.add_field(ConfigField(
        name='log_level',
        default_value='INFO',
        description='日志级别',
        type_hint=str,
        validation_rules=[
            ValidationRule(
                lambda x: x.upper() in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                '日志级别必须是DEBUG、INFO、WARNING、ERROR或CRITICAL之一'
            )
        ]
    ))
    
    schema.add_field(ConfigField(
        name='log_file',
        default_value='./logs/scraper.log',
        description='日志文件路径',
        type_hint=str
    ))
    
    # 网络配置
    schema.add_field(ConfigField(
        name='user_agent',
        default_value='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        description='用户代理字符串',
        type_hint=str
    ))
    
    schema.add_field(ConfigField(
        name='headers',
        default_value={},
        description='自定义请求头',
        type_hint=dict
    ))
    
    # 配置分组
    schema.add_group('basic', ['max_workers', 'timeout', 'retry_times', 'output_dir'])
    schema.add_group('logging', ['log_level', 'log_file'])
    schema.add_group('network', ['user_agent', 'headers'])
    
    return schema


# 全局配置管理器实例
_default_config_manager = None


def get_config_manager() -> ConfigManager:
    """获取默认配置管理器"""
    global _default_config_manager
    if _default_config_manager is None:
        _default_config_manager = ConfigManager(create_default_schema())
    return _default_config_manager


def get_config(key: str, default: Any = None) -> Any:
    """获取配置值（便捷函数）"""
    return get_config_manager().get(key, default)


def set_config(key: str, value: Any, validate: bool = True) -> bool:
    """设置配置值（便捷函数）"""
    return get_config_manager().set(key, value, validate)


def load_config_file(file_path: Union[str, Path]) -> bool:
    """加载配置文件（便捷函数）"""
    return get_config_manager().load_from_file(file_path)


def save_config_file(file_path: Optional[Union[str, Path]] = None) -> bool:
    """保存配置文件（便捷函数）"""
    return get_config_manager().save_to_file(file_path)