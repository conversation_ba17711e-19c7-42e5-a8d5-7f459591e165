#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
02-网站识别与解析模块测试脚本

本脚本用于测试02模块的基本功能，包括框架检测、菜单解析等核心功能。
主要验证Starlight框架支持是否正常工作。

测试内容:
1. 模块导入测试
2. 框架管理器初始化测试
3. Starlight框架注册测试
4. 基本功能接口测试

作者: Assistant
创建时间: 2024
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

def test_module_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        # 测试基础模块导入
        from frameworks.base import PageInfo, ContentInfo, DocumentFramework
        print("✓ 基础模块导入成功")
        
        # 测试Starlight框架导入
        from frameworks.starlight import StarlightFramework
        print("✓ Starlight框架导入成功")
        
        # 测试框架管理器导入
        from frameworks.manager import FrameworkManager, get_framework_manager
        print("✓ 框架管理器导入成功")
        
        # 测试包级导入
        from frameworks import detect_framework, parse_menu_structure, extract_page_content
        print("✓ 包级接口导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 导入过程中出现错误: {e}")
        return False

def test_framework_manager():
    """测试框架管理器"""
    print("\n=== 测试框架管理器 ===")
    
    try:
        from frameworks import get_framework_manager
        
        # 获取框架管理器实例
        manager = get_framework_manager()
        print(f"✓ 框架管理器实例创建成功: {manager}")
        
        # 检查已注册的框架
        frameworks = manager.get_registered_frameworks()
        print(f"✓ 已注册框架: {frameworks}")
        
        # 检查Starlight框架是否已注册
        if 'starlight' in frameworks:
            print("✓ Starlight框架已自动注册")
        else:
            print("✗ Starlight框架未注册")
            return False
        
        # 获取框架信息
        starlight_info = manager.get_framework_info('starlight')
        if starlight_info:
            print(f"✓ Starlight框架信息: {starlight_info}")
        else:
            print("✗ 无法获取Starlight框架信息")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 框架管理器测试失败: {e}")
        return False

def test_starlight_framework():
    """测试Starlight框架"""
    print("\n=== 测试Starlight框架 ===")
    
    try:
        from frameworks import get_framework_manager
        
        manager = get_framework_manager()
        
        # 获取Starlight框架实例
        starlight = manager.get_framework('starlight')
        if not starlight:
            print("✗ 无法获取Starlight框架实例")
            return False
        
        print(f"✓ Starlight框架实例: {starlight}")
        print(f"✓ 框架名称: {starlight.get_name()}")
        
        # 测试组件获取
        detector = starlight.get_detector()
        menu_parser = starlight.get_menu_parser()
        content_extractor = starlight.get_content_extractor()
        
        print(f"✓ 检测器: {detector.__class__.__name__}")
        print(f"✓ 菜单解析器: {menu_parser.__class__.__name__}")
        print(f"✓ 内容提取器: {content_extractor.__class__.__name__}")
        
        return True
        
    except Exception as e:
        print(f"✗ Starlight框架测试失败: {e}")
        return False

def test_data_classes():
    """测试数据类"""
    print("\n=== 测试数据类 ===")
    
    try:
        from frameworks.base import PageInfo, ContentInfo
        
        # 测试PageInfo
        page = PageInfo(
            url="https://example.com/docs/guide",
            title="用户指南",
            level=1,
            parent_path="文档",
            is_current=False,
            order=1
        )
        
        print(f"✓ PageInfo创建成功: {page}")
        print(f"✓ 完整路径: {page.get_full_path()}")
        
        # 测试ContentInfo
        content = ContentInfo(
            title="测试页面",
            content="<h1>测试内容</h1><p>这是一个测试页面。</p>",
            metadata={"description": "测试页面描述", "author": "测试作者"},
            url="https://example.com/test"
        )
        
        print(f"✓ ContentInfo创建成功: {content}")
        print(f"✓ 内容长度: {content.get_content_length()}")
        print(f"✓ 包含描述元数据: {content.has_metadata('description')}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据类测试失败: {e}")
        return False

def test_convenience_functions():
    """测试便捷函数"""
    print("\n=== 测试便捷函数 ===")
    
    try:
        from frameworks import detect_framework, parse_menu_structure, extract_page_content
        
        # 创建模拟HTML内容（包含Starlight特征）
        mock_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta name="generator" content="Astro v4.0.0">
            <title>测试文档</title>
        </head>
        <body>
            <div class="sl-nav-wrapper">
                <nav class="sidebar">
                    <ul>
                        <li><a href="/docs/guide">用户指南</a></li>
                        <li><a href="/docs/api">API参考</a></li>
                    </ul>
                </nav>
            </div>
            <main>
                <div class="sl-markdown-content">
                    <h1>测试页面</h1>
                    <p>这是测试内容。</p>
                </div>
            </main>
            <script>
                window.StarlightThemeProvider = {};
            </script>
        </body>
        </html>
        """
        
        test_url = "https://example.com/docs"
        
        # 测试框架检测
        detected_framework = detect_framework(test_url, mock_html)
        print(f"✓ 检测到框架: {detected_framework}")
        
        if detected_framework:
            # 测试菜单解析
            pages = parse_menu_structure(detected_framework, test_url, mock_html)
            print(f"✓ 解析到页面数量: {len(pages)}")
            
            # 测试内容提取
            content = extract_page_content(detected_framework, test_url, mock_html)
            if content:
                print(f"✓ 内容提取成功: {content.title}")
            else:
                print("! 内容提取返回None（可能是正常的）")
        
        return True
        
    except Exception as e:
        print(f"✗ 便捷函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试02-网站识别与解析模块")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_module_imports),
        ("框架管理器", test_framework_manager),
        ("Starlight框架", test_starlight_framework),
        ("数据类", test_data_classes),
        ("便捷函数", test_convenience_functions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✓ {test_name}测试通过")
            else:
                print(f"\n✗ {test_name}测试失败")
        except Exception as e:
            print(f"\n✗ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！02模块基本功能正常。")
        return True
    else:
        print(f"⚠️  有 {total - passed} 个测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)