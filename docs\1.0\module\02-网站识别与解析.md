# 网站识别与解析模块

## 模块概述
本模块负责识别和解析技术文档网站的框架类型和菜单结构，第一阶段专门支持Astro+Starlight框架。

## 功能要求

### 1. 框架识别
- **第一阶段**：专门支持Astro+Starlight框架
- **识别特征**：
  - 检测特征元素：`class="astro-*"`
  - JavaScript对象：`window.StarlightThemeProvider`
  - CSS类名：`.sidebar-sublist-wrapper`
  - 其他Starlight特有标识
- **扩展设计**：采用插件化架构，后续可轻松添加其他框架支持

### 2. Astro+Starlight菜单解析
- **菜单结构解析**：
  - 解析`.sidebar-sublist-wrapper ul li`结构获取菜单项
  - 识别`<details>`和`<summary>`标签处理折叠菜单
  - 提取`aria-current="page"`识别当前页面
  - 解析嵌套的`ul`标签获取子菜单层级
  - 支持多级嵌套菜单（最多5级深度）

### 3. 链接提取与处理
- **链接提取**：
  - 从`<a href="...">`标签提取页面链接
  - 自动补全相对路径为完整URL
  - 过滤外部链接和锚点链接
  - 去重处理，避免重复抓取

### 4. URL规范化
- **路径处理**：
  - 自动处理相对路径、绝对路径
  - 正确处理锚点链接
  - URL编码和解码
  - 路径标准化

### 5. 预览功能
- **菜单树状结构显示**：
  - 支持层级展开/折叠
  - 页面数量统计和预估时间
  - 与原网站菜单结构对比验证
  - 显示解析出的完整菜单树

## 技术实现

### 1. 核心依赖库
- **网络请求**：requests（简单可靠）
- **HTML解析**：BeautifulSoup4 + lxml（专门优化Starlight DOM解析）

### 2. 解析算法
- **DOM遍历**：深度优先搜索菜单结构
- **特征检测**：多重验证确保框架识别准确性
- **层级解析**：递归处理嵌套菜单结构
- **链接提取**：正则表达式和DOM选择器结合

### 3. 数据结构
- **菜单树**：树形结构存储菜单层级关系
- **页面信息**：URL、标题、层级、父子关系
- **解析结果**：统一的数据格式便于后续处理

## 质量保证

### 1. 解析准确性
- **多重验证**：使用多个特征元素确认框架类型
- **容错处理**：处理不规范的HTML结构
- **边界情况**：空菜单、单页面、深层嵌套等

### 2. 性能优化
- **缓存机制**：避免重复解析相同页面
- **增量解析**：只解析变化的部分
- **内存控制**：及时释放不需要的DOM对象

### 3. 扩展性
- **插件接口**：为其他框架预留扩展点
- **配置化**：解析规则可配置
- **模块化**：独立的解析器组件